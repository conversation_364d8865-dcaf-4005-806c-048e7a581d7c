{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b58f9935-978a-4172-b05e-dc113dcc26cb", "metadata": {}, "outputs": [], "source": ["#model\n", "import tensorflow as tf\n", "from sklearn.model_selection import train_test_split\n", "\n", "#nlp processing\n", "import unicodedata\n", "import re\n", "import numpy as np\n", "import pandas as pd\n", "\n", "import warnings \n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "code", "execution_count": 2, "id": "5ac14068-e22d-49b0-975f-b8ef067defc0", "metadata": {}, "outputs": [], "source": ["#reading data\n", "data=open('D:\\\\archive\\\\dialogs.txt','r').read()"]}, {"cell_type": "code", "execution_count": 3, "id": "0fd67ba1-e9ae-42e3-81b7-9fb9d0dce0de", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[['hi, how are you doing?', \"i'm fine. how about yourself?\"], [\"i'm fine. how about yourself?\", \"i'm pretty good. thanks for asking.\"], [\"i'm pretty good. thanks for asking.\", 'no problem. so how have you been?'], ['no problem. so how have you been?', \"i've been great. what about you?\"], [\"i've been great. what about you?\", \"i've been good. i'm in school right now.\"]]\n"]}], "source": ["#paried list of question and corresponding answer\n", "QA_list=[QA.split('\\t') for QA in data.split('\\n')]\n", "print(QA_list[:5])"]}, {"cell_type": "code", "execution_count": 4, "id": "bef9fea1-880b-4328-b308-37c2fd66f65e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['hi, how are you doing?', \"i'm fine. how about yourself?\", \"i'm pretty good. thanks for asking.\", 'no problem. so how have you been?', \"i've been great. what about you?\"]\n", "[\"i'm fine. how about yourself?\", \"i'm pretty good. thanks for asking.\", 'no problem. so how have you been?', \"i've been great. what about you?\", \"i've been good. i'm in school right now.\"]\n"]}], "source": ["questions=[row[0] for row in QA_list]\n", "answers=[row[1] for row in QA_list]\n", "\n", "print(questions[0:5])\n", "print(answers[0:5])"]}, {"cell_type": "code", "execution_count": 5, "id": "2cd694d4-112e-4ac3-9ff3-23386ce1c818", "metadata": {}, "outputs": [], "source": ["def remove_diacritic(text):\n", "    return ''.join(char for char in unicodedata.normalize('NFD',text)\n", "                  if unicodedata.category(char) !='Mn')"]}, {"cell_type": "code", "execution_count": 6, "id": "3b92a75f-090e-4a61-81ef-6c53b95b0cd9", "metadata": {}, "outputs": [], "source": ["def preprocessing(text):\n", "    \n", "    #Case folding and removing extra whitespaces\n", "    text=remove_diacritic(text.lower().strip())\n", "    \n", "    #Ensuring punctuation marks to be treated as tokens\n", "    text=re.sub(r\"([?.!,¿])\", r\" \\1 \", text)\n", "    \n", "    #Removing redundant spaces\n", "    text= re.sub(r'[\" \"]+', \" \", text)\n", "    \n", "    #Removing non alphabetic characters\n", "    text=re.sub(r\"[^a-zA-Z?.!,¿]+\", \" \", text)\n", "    \n", "    text=text.strip()\n", "    \n", "    #Indicating the start and end of each sentence\n", "    text='<start> ' + text + ' <end>'\n", "\n", "    return text"]}, {"cell_type": "code", "execution_count": 7, "id": "5d6f1261-26ba-4ffe-8219-68c9a3763b04", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<start> hi , how are you doing ? <end>\n", "<start> i m fine . how about yourself ? <end>\n"]}], "source": ["preprocessed_questions=[preprocessing(sen) for sen in questions]\n", "preprocessed_answers=[preprocessing(sen) for sen in answers]\n", "\n", "print(preprocessed_questions[0])\n", "print(preprocessed_answers[0])"]}, {"cell_type": "code", "execution_count": 8, "id": "1a4ff227-68e9-442b-922b-4777dca3cb5d", "metadata": {}, "outputs": [], "source": ["def tokenize(lang):\n", "    lang_tokenizer = tf.keras.preprocessing.text.Tokenizer(\n", "      filters='')\n", "    \n", "    #build vocabulary on unique words \n", "    lang_tokenizer.fit_on_texts(lang)\n", "    \n", "    return lang_tokenizer"]}, {"cell_type": "code", "execution_count": 9, "id": "09ee6031-7425-4eec-984b-ab30c159c688", "metadata": {}, "outputs": [], "source": ["def vectorization(lang_tokenizer,lang):\n", "    \n", "    #word embedding for training the neural network\n", "    tensor = lang_tokenizer.texts_to_sequences(lang)\n", "\n", "    tensor = tf.keras.preprocessing.sequence.pad_sequences(tensor,\n", "                                                         padding='post')\n", "\n", "    return tensor"]}, {"cell_type": "code", "execution_count": 10, "id": "bf163a2a-dcd3-47e6-a9dd-30209f64c820", "metadata": {}, "outputs": [], "source": ["def load_Dataset(data,size=None):\n", "    \n", "    if(size!=None):\n", "        y,X=data[:size]\n", "    else:\n", "        y,X=data\n", "        \n", "    X_tokenizer=tokenize(X)\n", "    y_tokenizer=tokenize(y)\n", "    \n", "    X_tensor=vectorization(X_tokenizer,X)\n", "    y_tensor=vectorization(y_tokenizer,y)\n", "    \n", "    return  X_tensor,X_tokenizer, y_tensor, y_tokenizer"]}, {"cell_type": "code", "execution_count": 11, "id": "bbc281d8-9a44-455b-868f-58c1d17b7b91", "metadata": {}, "outputs": [], "source": ["size=30000\n", "data=preprocessed_answers,preprocessed_questions\\\n", "\n", "X_tensor,X_tokenizer, y_tensor, y_tokenizer=load_Dataset(data,size)\n", "\n", "# Calculate max_length of the target tensors\n", "max_length_y, max_length_X = y_tensor.shape[1], X_tensor.shape[1]"]}, {"cell_type": "code", "execution_count": 12, "id": "f53dd6b5-0da9-481b-b6f1-291e6c48ff77", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2980 2980 745 745\n"]}], "source": ["X_train, X_val, y_train, y_val = train_test_split(X_tensor, y_tensor, test_size=0.2)\n", "\n", "# Show length\n", "print(len(X_train), len(y_train), len(X_val), len(y_val))"]}, {"cell_type": "code", "execution_count": 13, "id": "dcfcffbc-45a3-4b40-add5-d1f6c49b5e54", "metadata": {}, "outputs": [{"data": {"text/plain": ["(<PERSON><PERSON><PERSON><PERSON><PERSON>([64, 24]), <PERSON><PERSON><PERSON><PERSON><PERSON>([64, 24]))"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["BUFFER_SIZE = len(X_train)\n", "BATCH_SIZE = 64\n", "steps_per_epoch = len(X_train)//BATCH_SIZE\n", "embedding_dim = 256\n", "units = 1024\n", "vocab_inp_size = len(X_tokenizer.word_index)+1\n", "vocab_tar_size = len(y_tokenizer.word_index)+1\n", "\n", "dataset = tf.data.Dataset.from_tensor_slices((X_train, y_train)).shuffle(BUFFER_SIZE)\n", "dataset = dataset.batch(BATCH_SIZE, drop_remainder=True)\n", "\n", "example_input_batch, example_target_batch = next(iter(dataset))\n", "example_input_batch.shape, example_target_batch.shape"]}, {"cell_type": "code", "execution_count": 14, "id": "898f5317-8729-4a39-bd55-cdcd86517371", "metadata": {}, "outputs": [], "source": ["class Encoder(tf.keras.Model):\n", "    def __init__(self, vocab_size, embedding_dim, enc_units, batch_sz):\n", "        super(Encoder, self).__init__()\n", "        self.batch_sz = batch_sz\n", "        self.enc_units = enc_units\n", "        self.embedding = tf.keras.layers.Embedding(vocab_size, embedding_dim)\n", "        self.gru = tf.keras.layers.GRU(self.enc_units,\n", "                                       return_sequences=True,\n", "                                       return_state=True,\n", "                                       recurrent_initializer='glorot_uniform')\n", "\n", "    def call(self, x, hidden):\n", "        x = self.embedding(x)\n", "        output, state = self.gru(x, initial_state = hidden)\n", "        return output, state\n", "\n", "    def initialize_hidden_state(self):\n", "        return tf.zeros((self.batch_sz, self.enc_units))"]}, {"cell_type": "code", "execution_count": 15, "id": "075ac291-8602-489b-b37b-90c660d95dca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Encoder output shape: (batch size, sequence length, units) (64, 24, 1024)\n", "Encoder Hidden state shape: (batch size, units) (64, 1024)\n"]}], "source": ["encoder = Encoder(vocab_inp_size, embedding_dim, units, BATCH_SIZE)\n", "\n", "# sample input\n", "sample_hidden = encoder.initialize_hidden_state()\n", "sample_output, sample_hidden = encoder(example_input_batch, sample_hidden)\n", "print ('Encoder output shape: (batch size, sequence length, units) {}'.format(sample_output.shape))\n", "print ('Encoder Hidden state shape: (batch size, units) {}'.format(sample_hidden.shape))"]}, {"cell_type": "code", "execution_count": 16, "id": "6dba7044-4c66-44f8-a9ba-bf751cf9b0e4", "metadata": {}, "outputs": [], "source": ["class BahdanauAttention(tf.keras.layers.Layer):\n", "    def __init__(self, units):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.W1 = tf.keras.layers.Dense(units)\n", "        self.W2 = tf.keras.layers.Dense(units)\n", "        self.V = tf.keras.layers.Dense(1)\n", "\n", "    def call(self, query, values):\n", "        # query hidden state shape == (batch_size, hidden size)\n", "        # query_with_time_axis shape == (batch_size, 1, hidden size)\n", "        # values shape == (batch_size, max_len, hidden size)\n", "        # we are doing this to broadcast addition along the time axis to calculate the score\n", "        query_with_time_axis = tf.expand_dims(query, 1)\n", "\n", "        # score shape == (batch_size, max_length, 1)\n", "        # we get 1 at the last axis because we are applying score to self.V\n", "        # the shape of the tensor before applying self.V is (batch_size, max_length, units)\n", "        score = self.V(tf.nn.tanh(\n", "            self.W1(query_with_time_axis) + self.W2(values)))\n", "\n", "        # attention_weights shape == (batch_size, max_length, 1)\n", "        attention_weights = tf.nn.softmax(score, axis=1)\n", "\n", "        # context_vector shape after sum == (batch_size, hidden_size)\n", "        context_vector = attention_weights * values\n", "        context_vector = tf.reduce_sum(context_vector, axis=1)\n", "\n", "        return context_vector, attention_weights"]}, {"cell_type": "code", "execution_count": 17, "id": "88490a79-3b50-4d85-8300-d99a58bd3f19", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Attention result shape: (batch size, units) (64, 1024)\n", "Attention weights shape: (batch_size, sequence_length, 1) (64, 24, 1)\n"]}], "source": ["attention_layer = BahdanauAttention(10)\n", "attention_result, attention_weights = attention_layer(sample_hidden, sample_output)\n", "\n", "print(\"Attention result shape: (batch size, units) {}\".format(attention_result.shape))\n", "print(\"Attention weights shape: (batch_size, sequence_length, 1) {}\".format(attention_weights.shape))"]}, {"cell_type": "code", "execution_count": 18, "id": "6792e953-edb9-4da1-8169-d938162c0303", "metadata": {}, "outputs": [], "source": ["class Decoder(tf.keras.Model):\n", "    def __init__(self, vocab_size, embedding_dim, dec_units, batch_sz):\n", "        super(<PERSON><PERSON><PERSON>, self).__init__()\n", "        self.batch_sz = batch_sz\n", "        self.dec_units = dec_units\n", "        self.embedding = tf.keras.layers.Embedding(vocab_size, embedding_dim)\n", "        self.gru = tf.keras.layers.GRU(self.dec_units,\n", "                                       return_sequences=True,\n", "                                       return_state=True,\n", "                                       recurrent_initializer='glorot_uniform')\n", "        self.fc = tf.keras.layers.Dense(vocab_size)\n", "\n", "        # used for attention\n", "        self.attention = BahdanauAttention(self.dec_units)\n", "    \n", "    def call(self, x, hidden, enc_output):\n", "        # enc_output shape == (batch_size, max_length, hidden_size)\n", "        context_vector, attention_weights = self.attention(hidden, enc_output)\n", "\n", "        # x shape after passing through embedding == (batch_size, 1, embedding_dim)\n", "        x = self.embedding(x)\n", "\n", "        # x shape after concatenation == (batch_size, 1, embedding_dim + hidden_size)\n", "        x = tf.concat([tf.expand_dims(context_vector, 1), x], axis=-1)\n", "\n", "        # passing the concatenated vector to the GRU\n", "        output, state = self.gru(x)\n", "\n", "        # output shape == (batch_size * 1, hidden_size)\n", "        output = tf.reshape(output, (-1, output.shape[2]))\n", "\n", "        # output shape == (batch_size, vocab)\n", "        x = self.fc(output)\n", "\n", "        return x, state, attention_weights"]}, {"cell_type": "code", "execution_count": 19, "id": "ce471cf3-fff5-42c3-9f76-a36613accfda", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Decoder output shape: (batch_size, vocab size) (64, 2359)\n"]}], "source": ["decoder = Decoder(vocab_tar_size, embedding_dim, units, BATCH_SIZE)\n", "\n", "sample_decoder_output, _, _ = decoder(tf.random.uniform((BATCH_SIZE, 1)),\n", "                                      sample_hidden, sample_output)\n", "\n", "print ('Decoder output shape: (batch_size, vocab size) {}'.format(sample_decoder_output.shape))"]}, {"cell_type": "code", "execution_count": 20, "id": "fd69694c-2999-445b-ae06-a38c5e3fe131", "metadata": {}, "outputs": [], "source": ["optimizer = tf.keras.optimizers.Adam()\n", "loss_object = tf.keras.losses.SparseCategoricalCrossentropy(\n", "    from_logits=True, reduction='none')\n", "\n", "def loss_function(real, pred):\n", "    mask = tf.math.logical_not(tf.math.equal(real, 0))\n", "    loss_ = loss_object(real, pred)\n", "\n", "    mask = tf.cast(mask, dtype=loss_.dtype)\n", "    loss_ *= mask\n", "\n", "    return tf.reduce_mean(loss_)"]}, {"cell_type": "code", "execution_count": 21, "id": "d02bcf35-b0e1-493a-9e35-ddea34992fcb", "metadata": {}, "outputs": [], "source": ["def train_step(inp, targ, enc_hidden):\n", "    loss = 0\n", "\n", "    with tf.<PERSON><PERSON><PERSON><PERSON><PERSON>() as tape:\n", "        enc_output, enc_hidden = encoder(inp, enc_hidden)\n", "\n", "        dec_hidden = enc_hidden\n", "\n", "        dec_input = tf.expand_dims([y_tokenizer.word_index['<start>']] * BATCH_SIZE, 1)\n", "\n", "        # Teacher forcing - feeding the target as the next input\n", "        for t in range(1, targ.shape[1]):\n", "            # passing enc_output to the decoder\n", "            predictions, dec_hidden, _ = decoder(dec_input, dec_hidden, enc_output)\n", "\n", "            loss += loss_function(targ[:, t], predictions)\n", "\n", "            # using teacher forcing\n", "            dec_input = tf.expand_dims(targ[:, t], 1)\n", "    \n", "    batch_loss = (loss / int(targ.shape[1]))\n", "\n", "    variables = encoder.trainable_variables + decoder.trainable_variables\n", "\n", "    gradients = tape.gradient(loss, variables)\n", "\n", "    optimizer.apply_gradients(zip(gradients, variables))\n", "\n", "    return batch_loss"]}, {"cell_type": "code", "execution_count": 22, "id": "08bdb220-07fa-4a4a-81b0-a271f8aa4643", "metadata": {}, "outputs": [], "source": ["EPOCHS = 40\n", "\n", "for epoch in range(1, EPOCHS + 1):\n", "    enc_hidden = encoder.initialize_hidden_state()\n", "    total_loss = 0\n", "\n", "    for (batch, (inp, targ)) in enumerate(dataset.take(steps_per_epoch)):\n", "        batch_loss = train_step(inp, targ, enc_hidden)\n", "        total_loss += batch_loss\n", "\n", "    if(epoch % 4 == 0):\n", "        print('Epoch:{:3d} Loss:{:.4f}'.format(epoch,\n", "                                          total_loss / steps_per_epoch))"]}, {"cell_type": "code", "execution_count": null, "id": "9be2448a-3885-4a53-99c1-a65708a01ef2", "metadata": {}, "outputs": [], "source": ["def remove_tags(sentence):\n", "    return sentence.split(\"<start>\")[-1].split(\"<end>\")[0]"]}, {"cell_type": "code", "execution_count": null, "id": "362cd5dd-a7ec-4c84-aea6-296c1256cfcf", "metadata": {}, "outputs": [], "source": ["def evaluate(sentence):\n", "    sentence = preprocessing(sentence)\n", "\n", "    inputs = [X_tokenizer.word_index[i] for i in sentence.split(' ')]\n", "    inputs = tf.keras.preprocessing.sequence.pad_sequences([inputs],\n", "                                                         maxlen=max_length_X,\n", "                                                         padding='post')\n", "    inputs = tf.convert_to_tensor(inputs)\n", "\n", "    result = ''\n", "\n", "    hidden = [tf.zeros((1, units))]\n", "    enc_out, enc_hidden = encoder(inputs, hidden)\n", "\n", "    dec_hidden = enc_hidden\n", "    dec_input = tf.expand_dims([y_tokenizer.word_index['<start>']], 0)\n", "\n", "    for t in range(max_length_y):\n", "        predictions, dec_hidden, attention_weights = decoder(dec_input,\n", "                                                             dec_hidden,\n", "                                                             enc_out)\n", "\n", "        # storing the attention weights to plot later on\n", "        attention_weights = tf.reshape(attention_weights, (-1, ))\n", "\n", "        predicted_id = tf.argmax(predictions[0]).numpy()\n", "\n", "        result += y_tokenizer.index_word[predicted_id] + ' '\n", "\n", "        if y_tokenizer.index_word[predicted_id] == '<end>':\n", "            return remove_tags(result), remove_tags(sentence)\n", "\n", "        # the predicted ID is fed back into the model\n", "        dec_input = tf.expand_dims([predicted_id], 0)\n", "\n", "    return remove_tags(result), remove_tags(sentence)"]}, {"cell_type": "code", "execution_count": null, "id": "f70093ad-b506-411b-95d2-2d6504472ef4", "metadata": {}, "outputs": [], "source": ["def ask(sentence):\n", "    result, sentence = evaluate(sentence)\n", "\n", "    print('Question: %s' % (sentence))\n", "    print('Predicted answer: {}'.format(result))"]}, {"cell_type": "code", "execution_count": null, "id": "b8375ee4-2a62-4106-a319-9ac1acf6705f", "metadata": {}, "outputs": [], "source": ["for i in range(0, 5):\n", "    ask(questions[i])"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}