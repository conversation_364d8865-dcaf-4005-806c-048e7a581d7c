{"cells": [{"cell_type": "markdown", "metadata": {"collapsed": false}, "source": ["# Linear Regression Example\n", "\n", "A linear regression learning algorithm example using TensorFlow library.\n", "\n", "- Author: <PERSON><PERSON><PERSON>\n", "- Project: https://github.com/aymericdamien/TensorFlow-Examples/"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": true}, "outputs": [], "source": ["import tensorflow as tf\n", "import numpy\n", "import matplotlib.pyplot as plt\n", "rng = numpy.random"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": true}, "outputs": [], "source": ["# Parameters\n", "learning_rate = 0.01\n", "training_epochs = 1000\n", "display_step = 50"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": true}, "outputs": [], "source": ["# Training Data\n", "train_X = numpy.asarray([3.3,4.4,5.5,6.71,6.93,4.168,9.779,6.182,7.59,2.167,\n", "                         7.042,10.791,5.313,7.997,5.654,9.27,3.1])\n", "train_Y = numpy.asarray([1.7,2.76,2.09,3.19,1.694,1.573,3.366,2.596,2.53,1.221,\n", "                         2.827,3.465,1.65,2.904,2.42,2.94,1.3])\n", "n_samples = train_X.shape[0]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": true}, "outputs": [], "source": ["# tf Graph Input\n", "X = tf.placeholder(\"float\")\n", "Y = tf.placeholder(\"float\")\n", "\n", "# Set model weights\n", "W = tf.Variable(rng.randn(), name=\"weight\")\n", "b = tf.Variable(rng.randn(), name=\"bias\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": true}, "outputs": [], "source": ["# Construct a linear model\n", "pred = tf.add(tf.multiply(X, W), b)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"collapsed": true}, "outputs": [], "source": ["# Mean squared error\n", "cost = tf.reduce_sum(tf.pow(pred-Y, 2))/(2*n_samples)\n", "# Gradient descent\n", "optimizer = tf.train.GradientDescentOptimizer(learning_rate).minimize(cost)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"collapsed": false}, "outputs": [], "source": ["# Initialize the variables (i.e. assign their default value)\n", "init = tf.global_variables_initializer()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch: 0050 cost= 0.195095107 W= 0.441748 b= -0.580876\n", "Epoch: 0100 cost= 0.181448311 W= 0.430319 b= -0.498661\n", "Epoch: 0150 cost= 0.169377610 W= 0.419571 b= -0.421336\n", "Epoch: 0200 cost= 0.158700854 W= 0.409461 b= -0.348611\n", "Epoch: 0250 cost= 0.149257123 W= 0.399953 b= -0.28021\n", "Epoch: 0300 cost= 0.140904188 W= 0.391011 b= -0.215878\n", "Epoch: 0350 cost= 0.133515999 W= 0.3826 b= -0.155372\n", "Epoch: 0400 cost= 0.126981199 W= 0.374689 b= -0.0984639\n", "Epoch: 0450 cost= 0.121201262 W= 0.367249 b= -0.0449408\n", "Epoch: 0500 cost= 0.116088994 W= 0.360252 b= 0.00539905\n", "Epoch: 0550 cost= 0.111567356 W= 0.35367 b= 0.052745\n", "Epoch: 0600 cost= 0.107568085 W= 0.34748 b= 0.0972751\n", "Epoch: 0650 cost= 0.104030922 W= 0.341659 b= 0.139157\n", "Epoch: 0700 cost= 0.100902475 W= 0.336183 b= 0.178547\n", "Epoch: 0750 cost= 0.098135538 W= 0.331033 b= 0.215595\n", "Epoch: 0800 cost= 0.095688373 W= 0.32619 b= 0.25044\n", "Epoch: 0850 cost= 0.093524046 W= 0.321634 b= 0.283212\n", "Epoch: 0900 cost= 0.091609895 W= 0.317349 b= 0.314035\n", "Epoch: 0950 cost= 0.089917004 W= 0.31332 b= 0.343025\n", "Epoch: 1000 cost= 0.088419855 W= 0.30953 b= 0.370291\n", "Optimization Finished!\n", "Training cost= 0.0884199 W= 0.30953 b= 0.370291 \n", "\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAgkAAAFkCAYAAACq4KjhAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz\nAAAPYQAAD2EBqD+naQAAIABJREFUeJzt3Xl8lNXZ//HPNRiJgQAqUiyCCSA06lM1sSqyuKFQC0GL\n+DSK+1JrEX4stSpUYk20UgVjRatVK25prViFKvJoqRuktIa6EtwAUdqioMZhUSNzfn/MJGSSCclM\nZuaemXzfr9e85D5zL9ctIXPNOec+lznnEBEREWnK53UAIiIikpqUJIiIiEhEShJEREQkIiUJIiIi\nEpGSBBEREYlISYKIiIhEpCRBREREIlKSICIiIhEpSRAREZGIlCSIiIhIRO1KEszsajMLmNncVvab\nYGY1ZrbDzF4zs++357oiIiKSeDEnCWb2PeAS4LVW9hsCPAL8DjgceAJ4wswOjvXaIiIikngxJQlm\n1hV4CLgY+LyV3acAS5xzc51zbzvnZgOrgEmxXFtERESSI9aehPnAYufcsjbsOwR4rknb0lC7iIiI\npKg9oj3AzH5EcNjgyDYe0hvY1KRtU6i9pWvsC4wC1gNfRhujiIhIB5YN5AFLnXNb2nOiqJIEMzsA\nuBU42TlX147rGuB28/4o4OF2nF9ERKSjO5vgnMCYRduTUATsB1SbmYXaOgEjzGwS0Nk51/TD/7/A\nt5q09aJ570Jj6wEeeughCgoKogwx9UydOpV58+Z5HUbc6H5SVybdC+h+Ulkm3Qtk1v3U1NQwceJE\nCH2Wtke0ScJzwP80absfqAF+FSFBAKgCTgJua9R2cqi9JV8CFBQUUFhYGGWIqad79+4ZcR/1dD+p\nK5PuBXQ/qSyT7gUy735C2j1cH1WS4JzbBqxu3GZm24Atzrma0PYCYKNz7prQLhXAC2Y2DXgKKCHY\nI3FJO2MXERGRBIrHiotNew/60mhSonOuimBicCnwKvBDYJxzbjUiIiKSsqJ+uqEp59yJu9sOtS0E\nFrb3WiIiIpI8qt2QBCUlJV6HEFe6n9SVSfcCup9Ulkn3Apl3P/FikecaesvMCoHq6urqTJxIIiIi\nkjCrVq2iqKgIoMg5t6o952r3cIOISCbasGEDmzdv9joMkWZ69uxJv379knItJQkiIk1s2LCBgoIC\ntm/f7nUoIs3k5ORQU1OTlERBSYKISBObN29m+/btGbOgm2SO+oWSNm/erCRBRMRLmbKgm0is9HSD\niIiIRKQkQURERCJSkiAiIiIRKUkQERGRiJQkiIhIzEpLS/H5Yvsouf/++/H5fGzYsCHOUe3ywQcf\n4PP5eOCBB2I6PhkxpjIlCSIiHdDq1auZOHEiBxxwANnZ2fTp04eJEyeyenV0tffMLOYkwcwws5iO\nTZb2xFhZWUlFRUWcI0ouJQkiIh3M448/TmFhIX/729+48MILufPOO7n44ot5/vnnKSws5Mknn2zz\nuX7xi1/EvOjUueeey44dO5K2emCyPfLII2mfJGidBBGROHDOJexbcTzPvXbtWs4991wGDhzIiy++\nyD777NPw3pQpUxg2bBjnnHMOr7/+Onl5eS2eZ/v27eTk5ODz+dhzzz1jisXMYj5WkkM9CSIiMfL7\n/cyePJmR+fmc1rcvI/PzmT15Mn6/P2XPPWfOHHbs2MHdd98dliAA7LPPPvz2t79l69atzJkzp6G9\nft5BTU0NZ511Fvvssw/Dhw8Pe6+xL7/8ksmTJ7PffvvRrVs3TjvtNP7973/j8/n45S9/2bBfpPH+\nvLw8iouLWb58OUcffTR77bUXAwYM4MEHHwy7xmeffcaMGTP47ne/S25uLt27d+fUU0/l9ddfj/n/\nzerVqznxxBPJycmhb9++lJeXEwgEmu23aNEixowZQ58+fcjOzmbgwIGUlZWF7XvCCSfw1FNPNcyJ\n8Pl89O/fH4C6ujquvfZajjzySHr06EHXrl0ZMWIEzz//fMyxJ4p6EkREYuD3+xk/ZAjTamooDQQw\nwAFL589n/LJlLKyqIjc3N+XO/Ze//IW8vDyOPfbYiO+PGDGCvLw8/vKXv3DHHXcANPRiTJgwgUGD\nBnHjjTdSX0E40pj9eeedx2OPPca5557L0UcfzQsvvMAPfvCDZvtFOtbMePfdd5kwYQIXXXQR559/\nPvfddx8XXHABRx55ZMMy2WvXrmXRokVMmDCB/Px8Nm3axF133cXxxx/P6tWr6d27d1T/XzZt2sTx\nxx9PIBDgmmuuIScnh7vvvpvs7Oxm+95///3k5uYyffp0unbtyrJly7j22mvx+/3cdNNNAMyaNYva\n2lo2btzIrbfeinOOrl27AvDFF19w3333UVJSwqWXXorf7+fee+9l9OjR/OMf/+C73/1uVLEnlHMu\n5V5AIeCqq6udiEiyVVdXu9Z+B117xRVuic/nHDR7Pe3zudmTJ8d8/USdu7a21pmZO/3003e737hx\n45zP53Nbt251zjlXWlrqzMydffbZzfYtLS11Pp+vYXvVqlXOzNz06dPD9rvgggucz+dz1113XUPb\n/fff73w+n/vggw8a2vLy8pzP53PLly9vaPvkk09cdna2+9nPftbQ9vXXXzeL5YMPPnDZ2dmurKys\noW39+vXOzNyCBQt2e8//7//9P+fz+dwrr7zS0LZ582bXo0ePZjF++eWXzY6/7LLLXNeuXcPiGjNm\njMvPz2+2byAQcHV1dWFttbW1rnfv3u7iiy/ebZxt+dms3wcodO38PNZwg4hIDJYvXsyoCF3RAKMD\nAZYvWpRy564fqmitF6L+/S+++KKhzcy47LLLWr3GM888g5nxk5/8JKz9iiuuaOh9aM3BBx8c1tPR\ns2dPBg8ezNq1axvasrKyGv4cCAT49NNPycnJYfDgwaxatapN12lsyZIlHHPMMRQVFTW07bvvvpx9\n9tnN9u3cuXPDn7du3cqWLVsYNmwY27dvZ82aNa1ey8zYY49gR75zjs8++4yvv/6aI488MqbYE0lJ\ngohIlJxzdKmro6WphAbk1NW1+UMxWeeu//BvbV5DS8lEfn5+q9eoH4Nvuu/AgQPbHGekpx323ntv\nPvvss4Zt5xzz5s1j0KBBdO7cmZ49e9KrVy/eeOMNamtr23ytxnEfdNBBzdoHDx7crG316tWcfvrp\n9OjRg27durHffvtxzjnnALT52gsWLOCwww4jOzubfffdl169evHUU0/FFHsiaU6CiEiUzIxtWVk4\niPhh7oBtWVkxPZGQyHN369aN/fffv9XJfa+//jp9+vRpGEOvt9dee0V9zXrRxNupU6eI7Y0To/Ly\ncq699louuugiysrK2GefffD5fEyZMiXiZMNYY2yajNXW1jJixAh69OhBWVkZ/fv3Jzs7m+rqaq66\n6qo2Xfuhhx7iggsu4Ic//CFXXnklvXr1olOnTtxwww1hvSWpQEmCiEgMho4dy9L58xkd4UPhGZ+P\nYcXFKXnuMWPGcM8997BixYqIkxdfeukl1q9f32y4oK0OPPBAAoEA69atY8CAAQ3t77zzTswxR7Jw\n4UJOPPFEfve734W1f/755+y3335Rn+/AAw+MGOPbb78dtv3888/z2Wef8eSTTzJ06NCG9vfff7/Z\nsS0lRgsXLmTAgAE89thjYe3XXntt1HEnmoYbRERiMKO8nLkFBSzx+aj/rumAJT4f8woKmF5WlpLn\n/tnPfkZ2djY//vGP+fTTT8Pe+/TTT7nsssvo0qULM2bMiOn8o0aNwjnX8GREvd/85jdxXUeiU6dO\nzb7l/+lPf2Ljxo0xne/UU0/l73//O6+88kpD2yeffEJlZWXE6zbuMfj666+b3S9Aly5dIg4fdOrU\nqdn/i5UrV1JVVRVT7ImkngQRkRjk5uaysKqKW2bNYu6iReTU1bE9K4uhxcUsLCuL+RHFRJ974MCB\nLFiwgIkTJ/I///M/XHTRReTn57Nu3Truu+8+tmzZwh/+8Ic2zT+IpLCwkPHjx3PrrbeyefNmjjnm\nGF544QXeffddILphh90ZM2YM119/PRdeeCHHHnssb7zxBg8//HBY70U0rrzySh588EFGjRrFlClT\nyMnJ4Xe/+x0HHnhg2PDMsccey9577825557L5MmTgeDwQaT7Kioq4tFHH2X69Ol873vfo2vXrowZ\nM4YxY8bw+OOPc9ppp/GDH/yAtWvXctddd3HIIYewdevW2P6HJEp7H49IxAs9AikiHmrLY2ZNBQKB\nhMWTiHO/+eab7uyzz3Z9+vRxnTt3dt/+9rfdxIkT3VtvvdVs3/rHHLds2RLxvU6dOoW17dixw11x\nxRWuZ8+erlu3bm78+PHu3XffdWbm5syZ07BfpEcg8/PzXXFxcbPrHH/88e7EE09s2P7qq6/cz372\nM9enTx/XpUsXN2LECLdy5Up3wgknhO23fv165/P5Wn0Esv7/yQknnOBycnJc37593Q033ODuu+++\nZjFWVVW5Y4891nXp0sUdcMAB7uqrr3bPPvus8/l87oUXXmjYb9u2bW7ixIlun332cT6fL+xxyF/9\n6lcuPz/f7bXXXq6oqMg9/fTT7vzzz3f9+/ffbYzJfgTSXAwzZBPNzAqB6urqagoLC70OR0Q6mFWr\nVlFUVIR+B8XPq6++SmFhIQ8//DAlJSVeh5O22vKzWb8PUOSca9czlZqTICIicfXVV181a7v11lvp\n1KkTI0aM8CAiiZXmJIiISFzNmTOH6upqjj/+ePbYYw+efvppli5dyo9//GP69OnjdXgSBSUJIiIS\nV0OGDOHZZ5+lrKyMrVu30q9fP6677jquueYar0OTKClJEBGRuBo5ciQjR470OgyJA81JEBERkYiU\nJIiIiEhEShJEREQkIiUJIiIiEpGSBBEREYlISYKIiIhEpCRBREREIlKSICIizRxwwAFceumlnsbw\n/vvv4/P5eOSRR3a731//+ld8Ph8rVqxoaJs4cSIHHXRQokPMeEoSREQ6kAULFuDz+SK+Gq+I6PP5\nwsofv/XWW1x33XV89NFHzc45f/58HnzwwaTE35KmpZrNDJ9PH3HtpRUXRUQ6GDPj+uuvJy8vL6z9\n0EMPbfjz+++/T6dOnRq233zzTa677jpOPvlkDjjggLDjbr/9dvr27cs555yT0Lijcf/995OKVY7T\njZIEEZEOaPTo0bstg52VlRW27Zxr9m09lTVOcCR26osREZFmGs9JuPfeeznrrLMAGDZsGD6fj06d\nOrFixQr69u3L22+/zXPPPdcwbHHKKac0nOfzzz9n8uTJ9OvXj+zsbAYNGsTNN9/c7HqfffYZ5557\nLj169GCfffbhoosu4osvvog5/qZzEurnN9x2223cddddDBgwgL322otjjjmGf/3rX82Or6mpYfz4\n8ey7777k5ORw1FFH8fTTT8ccT7qKqifBzC4DfgLkhZreAn7pnHumhf3PA34POKA+Bf3SOZcTU7Qi\nIhIXtbW1bNmyJaxt3333bfhz416DE044gZ/+9KfccccdzJ49u+HDd/Dgwdx+++1cfvnl7Lvvvlx9\n9dU459h///0B2L59O8OHD+fjjz/msssu44ADDuDll1/myiuv5OOPP2bOnDlAsJdi7NixrFy5kssv\nv5zBgwezcOFCLrjggph7L8ws4rELFixg+/btXH755TjnuOmmm/jhD3/YkEQAvPHGGwwfPpwDDzyQ\nq6++mpycHP74xz9SXFzME088wZgxY2KKKR1FO9zwIfBz4L3Q9vnAk2Z2uHOupoVjaoFB7EoSNEgk\nIuIh5xwnnXRSWJuZsXPnzoj79+/fn2HDhnHHHXdw8sknc+yxxza8N27cOK666ip69+5NSUlJ2HFz\n5sxhw4YNvPbaaw3zHy655BK+9a1vUVFRwbRp0+jduzePP/44K1as4NZbb2Xy5MkAXHbZZYwYMSKO\ndx20ceNG3nvvPbp27QrAgAEDOOOMM3juuecaekCuuOIKBg4cyMqVKxuGLS6//HKOOeYYrrrqKiUJ\nLXHOPdWkaZaZ/QQ4BmgpSXDOuU9iCU5EJB1s3w5r1iT2Gt/5DuTEqQ/WzLjjjjsS/ojgY489xvHH\nH09ubm5Yr8XIkSO5+eabeemll5gwYQJPP/00nTt3Dnvk0ufzMWnSpLDHGuPhrLPOakgQAIYPH45z\njrVr1wKwefNmXnzxRX71q1/x+eefN+znnGPUqFGUlZXxySefsN9++8U1rlQV88RFM/MBZwI5QNVu\ndu1qZusJzn9YBVzjnFsd63VFRFLNmjVQVJTYa1RXw27mGUbte9/73m4nLsbDu+++S01NTcQPVDPj\n448/BmDDhg306dOH7OzssH0GDx4c95j69u0btr333nsDwTkR9TEDXH311Vx11VUtxq0koQVmdijB\npCAb8AOnO+dayqHfBi4EXge6Az8DVpjZIc65jbGFLCKSWr7zneCHeKKvkW6cc4wePZrp06dHfL8+\nCWjpyYlEPMLY0lMP9dcKBAIA/PznP2fkyJER983Pz497XKkqlp6ENcBhQA9gPPCAmY2IlCg45/4O\n/L1+28yqCA5LXArMbu1CU6dOpXv37mFtJSUlzca9RES8lJMT32/5qWh3Ewhbeq9///5s27aNE088\ncbfnzsvLY/ny5Xz55ZdhvQlvv/12bMG2w4ABAwDYc889W43bS9u2bQOgsrKSysrKsPdqa2vjdp2o\nkwTn3DfA2tDmKjM7CphC8KmHVo81s38BA9tyrXnz5iW8O0xERFrXpUsXnHNh4/SN34vUfuaZZ1Je\nXs6yZcuafeB+/vnndOvWDZ/Px6mnnsp9993HXXfdxZQpUwDYuXMnt99+e9LXZujduzfDhg3jzjvv\n5PLLL6dXr15h72/evJmePXsmNaZIfnb++Tz76qsRvzivWrWKojiNf8VjMSUf0LktO4bmMRwKdLyH\nTUVEUkQs3fhHHHEEPp+PG2+8kc2bN9O5c2dOPvlk9tlnH4qKirj33nu54YYbGDBgAL179+a4447j\nqquuYvHixXz/+9/nggsu4IgjjmDr1q28/vrrPP7442zcuJFu3bpx+umnc8wxxzBjxgzef//9hkcg\nt2/fntB7asmdd97JiBEjOPTQQ7nkkkvIz89n06ZNrFixgk2bNvHKK6/E7VqxOnvdOm6ZNYvSioqE\nXifadRLKgSUEH4XMBc4GjgNOCb3/APCRc+6a0PYvCA43vEdweOJK4EDgnjjFLyIiUWrLt/Om6wx8\n+9vf5s477+Smm27i4osvZufOnbz00ksce+yxlJaW8tFHH3HTTTexdetWTjrpJI477jhycnJ4+eWX\nKS8v57HHHmPBggV0796dQYMGUVZW1vCUgZnx1FNPMWXKFB544AE6derEaaedxi233MKRRx4Z8z1F\nqufQ0n6N2w855BBeeeUVSktL+f3vf89nn31Gr169OOKII7j22mvbFE+iHescVy1aBAlOEiya7MvM\n7gFOBPYnuP7B68CvnHPLQu8vA9Y75y4Mbc8FTgd6A58B1cBM59zrrVynEKiurq7WcIOIJF19d61+\nB0mqafjZBK7r04cnPvywWeLTaLihyDm3qj3Xi3adhItbef/EJtvTgGkxxCUiIiItcMC2rKyEz9lQ\n7QYREZE0s8KMYcXFCb+OqkCKiIikmYfz83m2rCzh11FPgoiISJr59f33k5ubm/DrKEkQERFJM126\ndEnKdZQkiIiISERKEkRERCQiJQkiIiISkZ5uEBFpQU1NjdchiIRJ9s+kkgQRkSZ69uxJTk4OEydO\n9DoUkWZycnKSVmRKSYKISBP9+vWjpqaGzZs3ex2KNLFuHZxxRnhbdTVcNmYMd/7nP0Raf9ABP9l/\nf377l78kI8SE69mzJ/369UvKtZQkiIhE0K9fv6T9Ipa2aboC8caN8O1vB//8/TPO4JP58xkdCDQ7\nbonPx6kTJqgORww0cVFERFLab34TniBMnQrO7UoQAGaUlzO3oIAlPh/1ZQsdwQRhXkEB05OwOmEm\nUk+CiIikJL8funULbwsEmvcoAOTm5rKwqopbZs1i7qJF5NTVsT0ri6HFxSwsK0vK6oSZSEmCiIik\nnEMOgdWrd22/+CIMH777Y3JzcymtqICKCpxzCa+Q2BEoSRARkZTx8svhyUBBQXiy0FZKEOJDSYKI\niHjOOfA1mSVXW9t8uEGSSxMXRUTEUzNmhCcIt94aTBqUIHhPPQkiIuKJ//wn/AkFCCYHkjrUkyAi\nIknn84UnCG+9pQQhFSlJEBGRpFm4MPgIY31CUFwc/PPBB3sbl0Sm4QYREUm4L7+EvfYKb/vqK9hz\nT2/ikbZRT4KIJJRTH3KHZxaeIDz6aLD3QAlC6lOSICJx5/f7mT15MiPz8zmtb19G5ucze/Jk/H6/\n16FJEr38cvPVEZ2DCRO8iUeip+EGEYkrv9/P+CFDmFZTQ2kggBFcQ3/p/PmMX7aMhVVVWiK3A2ia\nHKxZA4MHexOLxE49CSISVzfPnMm0mhpGhxIEAANGBwJMranhllmzvAxPEuycc8IThEMOCfYeKEFI\nT0oSRCSuli9ezKgI5XohmCgsX7QoyRFJMmzeHEwOHnpoV9vOnfDmm97FJO2nJEFE4sY5R5e6Olpa\nNd+AnLo6TWbMMGaw3367th96KPIyy5J+NCdBROLGzNiWlYWDiImCA7ZlZan4ToaorISzzgpvU/6X\nWZTniUhcDR07lqUtfIV8xudjWHFxkiOSeAsEgr0HjROEjz9WgpCJlCSISFzNKC9nbkEBS3w+6j8z\nHLDE52NeQQHTy8q8DE/a6fDDoVOnXds/+lEwOWg83CCZQ8MNIhJXubm5LKyq4pZZs5i7aBE5dXVs\nz8piaHExC8vK9Phjmnr3XRg0KLxNPQeZT0mCiMRdbm4upRUVUFGBc05zENJc07++55+H447zJBRJ\nMg03iEhCKUFIX+XlkVdMVILQcagnQUREwkQqxrRjB2RnexOPeEc9CSIi0qBpMabS0mDvgRKEjkk9\nCSIiwssvw/Dh4W2amChKEkREOjgVY5KWaLhBRKSDUjEmaY16EkREOpjNm5svfrRzp2otSHP6kRAR\n6UBUjEmiEdWPhZldZmavmVlt6LXCzEa3cswEM6sxsx2hY7/fvpBFRCRalZWR1zw4+2xv4pH0EO1w\nw4fAz4H3QtvnA0+a2eHOuZqmO5vZEOCR0DFPAWcBT5jZEc651TFHLSIibRIIhNdagGAxJtVakLaI\nqifBOfeUc+4Z59x7odcsYCtwTAuHTAGWOOfmOufeds7NBlYBk9oXtoiItEbFmKS9Yp64aGY+4Ewg\nB6hqYbchwC1N2pYC42K9roiI7J6KMUm8RJ0kmNmhBJOCbMAPnO6cW9PC7r2BTU3aNoXaRUQkzlSM\nSeIplvmsa4DDgKOBO4EHzOw7URxvgHJaEZE4UjEmSYSoexKcc98Aa0Obq8zsKIJzD34SYff/At9q\n0taL5r0LEU2dOpXu3buHtZWUlFBSUhJVzCIimSpSMabt25u3SWaqrKyksrIyrK22tjZu5zfXzoEq\nM/sr8IFz7sII7/0B2Ms5N65R23LgNefc5bs5ZyFQXV1dTWFhYbviExHJVE17DkpLYfZsT0JpM+ec\nyocn2KpVqygqKgIocs6tas+5oupJMLNyYAnBRyFzgbOB44BTQu8/AHzknLsmdEgF8IKZTSP4CGQJ\nUARc0p6gRUQ6snQrxuT3+7l55kyWL15Ml7o6tmVlMXTsWGaUl5Obm+t1eLIb0Q43fAt4ANgfqAVe\nB05xzi0LvX8A8E39zs65KjMrAcpDr3eBcVojQUQkNulWjMnv9zN+yBCm1dRQGgg0TEpbOn8+45ct\nY2FVlRKFFBbtOgkXO+f6O+f2cs71ds41ThBwzp3YdNjBObfQOfed0DHfdc4tjVfwIiIdRdNiTAcf\nnB7FmG6eOZNpNTWMDiUIEJy9PjoQYGpNDbfMmuVleNIKrdYtIpIEsc7/2rIlmBw89NCutp074a23\n4hRYgi1fvJhRgUDE90YHAixftCjJEUk0lCSIiCSI3+9n9uTJjMzP57S+fRmZn8/syZPx+/1tOt4M\nevbctf3gg+lVjMk5R5e6OlqapmhATl1dzAmUJJ5KRYuIJEB7xuIrK+Gss8Lb0vFz1MzYlpWFg4iJ\nggO2ZWXpaYcUlib5qIhIeollLN65YO9B4wTh44/TM0GoN3TsWJa20PXxjM/HsOLiJEck0VCSICKS\nANGOxR9+ePgwQqYUY5pRXs7cggKW+HwNS+06YInPx7yCAqaXlXkZnrRCww0iInEWzVj8e+9ZRhdj\nys3NZWFVFbfMmsXcRYvIqatje1YWQ4uLWVhWpscfU5ySBBGROGvrWLzPF/5uphZjys3NpbSiAioq\ntOJimtFwg4hIAuxuLP4Cu4a/rl8X1tZRijEpQUgv6kkQkZSXjt8+Z5SXM37ZMlyjyYs76EwOX4bV\nwVUxJkll6kkQkZTU3jUGvFY/Fr9y0iROycvDcMEEIaS0NNh7oARBUpl6EkQk5WTKev+5ubkcfkIF\n191WEdaeSRMTJbOpJ0FEUk6mrPdvBqefvmt7zRolCJJelCSISMpJ9/X+Bw9uXq0xHYoxiTSl4QYR\nSSnRrDGQapMZ//1v6NMnvG3nzvSptSDSlH50RSSlNF5jIJJUXe/fLDxBKC9Pr2JMIpHox1dSgqrA\nSWPptN7/TTdFHlq45hpv4hGJJw03iGf8fj83z5zJ8sWL6VJXx7asLIaOHcuM8vK0mLkuiRNpjQFH\nMEGYV1DAwhRY7z8QgE6dwts+/BAOOMCbeEQSQUmCeCJTHnGTxEj19f6b9hzk5cG6dRF3FUlrShLE\nE40fcatX/4ibCz3iVlpR0fIJJOOl4nr///oXFBaGt2mkTDKZ5iSIJ9L9ETdJrlRIEMzCE4THHlOC\nIJlPSYIkXTSPuIl47ZxzIk9MHD/em3hEkknDDZJ0bS2jmwrfHqXj2r4dunQJb/P7oWtXb+IR8YJ6\nEsQT6fSIm3Q8ZuEJwoQJwd4DJQjS0ShJEE/MKC9nbkEBS3y+hkVzHLAk9Ijb9BR4xE06nnvuiTy0\n8Oij3sQj4jUNN4gnUv0RN+l4miYH//wnHHmkN7GIpAolCeKZVHzETTqeSD92mjMrEqThBkkJShAk\n2davb57pEtg+AAAdnElEQVQgfPONEgSRxpQkiEiHYwb5+bu2L700mBw0XWZZpKNTkiAiHcYVV0Se\nmHjXXd7EI5LqNCdBRDJepGJM778P/ft7E49IulCSICIZTRMTRWKn4QYRyUgvvRR5aEEJgkjbKUkQ\n6YAyvS6GGYwYsWv7zjuVHIjEQsMNIh2E3+/n5pkzWb54MV3q6tiWlcXQsWOZUV6eMYtXfe978Mor\n4W1KDkRipyRBpAPw+/2MHzKEaTU1lAYCGMFlsJfOn8/4ZctYWFWV1omC3w/duoW31dY2bxOR6Gi4\nQaQDuHnmTKbV1DA6lCBAsALn6ECAqTU13DJrlpfhtYtZeDJw6KHB3gMlCCLtpyRBpANYvngxowKB\niO+NDgRYvmhRkiNqv3vvjTwx8Y03vIlHJBNpuEEkwznn6FJXR0sLXxuQU1eXVvUzmoa5bBmccII3\nsYhkMiUJIhnOzNiWlYWDiImCA7ZlZaVFgpCsNQ/SKWESSSQNN4h0AEPHjmWpL/I/92d8PoYVFyc5\nouh88EHiizH5/X5mT57MyPx8Tuvbl5H5+cyePBm/3x+/i4ikGSUJIh3AjPJy5hYUsMTno/5z1QFL\nfD7mFRQwvazMy/B2ywzy8nZtX3JJ/Isx1T/9MWT+fJ5dv54nN27k2fXrGTJ/PuOHDFGiIB1WVEmC\nmV1tZv8wsy/MbJOZ/dnMBrVyzHlmFjCznaH/Bsxse/vCFpFo5ObmsrCqipWTJnFKXh7j+vThlLw8\nVk6alLKPP7ZUjOnuu+N/rUx++kOkPaKdkzAc+A3wSujYG4H/M7MC59yO3RxXCwxi15ColjcRSbLc\n3FxKKyqgoiKlx9wjFWN67z0YMCBx11y+eDGlu3n6Y+6iRVBRkbgARFJUVEmCc+7Uxttmdj7wMVAE\nvLz7Q90nUUcnIgmRqgmCF8WYMvHpD5F4ae+chB4EewU+bWW/rma23sw2mNkTZnZwO68rIhlkyRLv\nijE1fvojknR6+kMk3mJOEiz4L+ZW4GXn3Ord7Po2cCFQDJwduuYKM+sT67VFJHOYwamN+ijvuCP5\n9RbS/ekPkUSxWKvBmdmdwChgqHPuP1EctwdQAzzinJvdwj6FQPWIESPo3r172HslJSWUlJTEFLOI\npI6uXWHbtvA2r4ox1T/dMLXR5EVHMEGYV1CQspM7RSorK6msrAxrq62t5cUXXwQocs6tas/5Y0oS\nzOx2YCww3Dm3IYbjHwXqnHNnt/B+IVBdXV1NYWFh1PGJSOqqrYUePcLbPv0U9t7bm3jq+f1+bpk1\ni+WLFpFTV8f2rCyGFhczvaxMCYKklVWrVlFUVARxSBKiXnExlCCMA46LMUHwAYcCT0d7rIikt6bD\n+j4f7NzpTSxNpcvTHyLJFO06CXcQnFdwFrDNzL4VemU32meBmd3QaPsXZnaymeWb2RHAw8CBwD3x\nuQURSXU33RR5YmKqJAhNKUEQCYq2J+EygkN1zzdpvwB4IPTnvkDjf/p7A3cDvYHPgGpgiHNuTbTB\nimSqTP7m2vS2nnwSNA9QJD1Eu05Cqz0PzrkTm2xPA6ZFGZdIxvP7/dw8cybLFy+mS10d27KyGDp2\nLDPKyzNiDNyLNQ9EJL5UBVLEA/Wz6afV1FDaaDb90vnzGb9sWVrPpn/nHRg8OLztm2/iW2tBRJJD\nBZ5EPJCptQLMwhOEUaPiX4xJRJJHSYKIB5YvXsyo3dQKWL5oUZIjap/TTos8MfGZZ7yJR0TiQ8MN\nIkmWSbUCIhVjeustOFgLr4tkBCUJIknWuFZApBQgXWoFaGKiSObTcIOIB9K5VoCXxZhEJLmUJIh4\nYEZ5OXMLClji8zVUH3TAklCtgOllZV6G16KmxZiuu07JgUgm03CDiAdyc3NZWFXFLbNmMbdJrYCF\nKVgrIJWKMYlI8ihJEPFIOtQKSNViTCKSHEoSRFJAKiYITUMyCz7NICIdh+YkiEiYOXMiT0xUgiDS\n8agnQUQaqBiTiDSmJEFEtOaBiESk4QaRDuzdd5snCN98owRBRIKUJIh0UGYwaNCubRVjEpGmlCSI\ndDAqxiQibaU5CSIdhIoxiUi0lCSIdACamCgisdBwg0gGe+klFWMSkdgpSRDJUGYwYsSu7TvuUHIg\nItHRcINIhjnqKPjnP8PblByISCyUJIhkiK1boWnxyM8/h+7dvYlHRNKfhhtEMoBZeIJwyCHB3gMl\nCCLSHkoSRNLYffdFnpj45pvexCMimUXDDSJpqmlysGwZnHCCN7GISGZSkiCSZrTmgYgki4YbRNLE\nBx+oGJOIJJeSBJE0YAZ5ebu2L7lExZhEJPGUJIiksMmTI09MvPtub+IRkY5FcxJEUlCkYkzvvQcD\nBngTj4h0TEoSRFKMJiaKSKrQcINIilAxJhFJNUoSRJLAtfJJr2JMIpKKlCSIJIjf72f25MmMzM/n\ntL59GZmfz+zJk/H7/Q37nHtu5N6Dn/wkycGKiESgOQkiCeD3+xk/ZAjTamooDQQwwAFL589n/LJl\nPPjXKnr3Dq/GpGJMIpJq1JMgkgA3z5zJtJoaRocSBAADRgcCPPvWm2EJwoQJKsYkIqlJSYJIAixf\nvJhRgUBY25MUY4RPNHAOHn00mZGJiLSdhhtE4sw5R5e6OhpPNWiaHAzf7we8sOkvQITnHUVEUoR6\nEkTizMzYlpWFAwpY3SxBCGDs2WU1FmlBBBGRFKIkQSQBvnvi2fhwrKGgoe0bOuEwnvH5GFZc7GF0\nIiJto+EGkTgLdhCUNWxfz0xmcQMOWOLzMa+ggIVlZS0dLiKSMqLqSTCzq83sH2b2hZltMrM/m9mg\nNhw3wcxqzGyHmb1mZt+PPWSR1HTzzc3XPJg9eQov5D3CuD59OCUvj5WTJrGwqorc3NzIJxERSSHR\n9iQMB34DvBI69kbg/8yswDm3I9IBZjYEeAT4OfAUcBbwhJkd4ZxbHXPkIikiUjGmDRugb1+ACqio\nwDmnOQgiknaiShKcc6c23jaz84GPgSLg5RYOmwIscc7NDW3PNrNTgEnA5VFFK5Jimn7u9+sHH3wQ\naT8lCCKSfto7cbEHwYXkPt3NPkOA55q0LQ21i6Sl996LvJxypARBRCRdxZwkWPCr0a3Ay60MG/QG\nNjVp2xRqF0k7ZnDQQbu2Fy5UMSYRyUztebrhDuBgYGgMx9YvZb9bU6dOpXuTtWpLSkooKSmJ4ZIi\n7XPjjXDNNeFtSg5ExEuVlZVUVlaGtdXW1sbt/NZaCduIB5ndDowFhjvnNrSy7wfALc652xq1lQLj\nnHNHtHBMIVBdXV1NYWFh1PGJxNNXX0F2dnjb9u2w117exCMisjurVq2iqKgIoMg5t6o954p6uCGU\nIIwDTmgtQQipAk5q0nZyqF0kpZmFJwizZwd7D5QgiEhHENVwg5ndAZQAxcA2M/tW6K1a59yXoX0W\nABudc/UdsxXAC2Y2jeAjkCUEn4a4JA7xiyTE8uUwbFh4m4YWRKSjibYn4TKgG/A88O9GrzMb7dOX\nRpMSnXNVBBODS4FXgR8SHGrQGgmSkszCE4Q1a5QgiEjHFO06Ca0mFc65EyO0LQQWRnMtkWQ77zx4\n4IFd2wUFsFqprIh0YKrdIB3eli3Qs2d4286d4FP5MxHp4PRrUDo0s/AE4YEHgkMLShBERNSTIB3U\nH/8IP/pReJvmHYiIhFOSIB1KpF6Cjz+G/fbzJh4RkVSmTlXpMAoLwxOE//3fYNKgBEFEJDL1JEjG\ne++98FoLoKEFEZG2UE+CZLSmxZief14JgohIWylJkIx0442RSzkfd5w38YiIpCMNN0hGUTEmEZH4\nUU+CZAwVYxIRiS/1JEjaUzEmEZHEUJIgaa3pvIOaGvjOd7yJRUQk02i4QdLS+eeHJwgFBcHeAyUI\nIiLxo54ESSsqxiQikjz61SppQ8WYRESSSz0JkvJUjElExBtKEiRlqRiTiIi31FErKamoKDxBOPNM\nFWMSEUk29SRISnn/fRg4MLxNQwsiIt5QT4KkDLPwBOFvf1OCICLiJSUJacBl+CdlS8WYjj/ek3BE\nRCREww0pyu/3c/PMmSxfvJgudXVsy8pi6NixzCgvJzc31+vw4kLFmKQp5xzWNGMUEc+oJyEF+f1+\nxg8ZwpD583l2/Xqe3LiRZ9evZ8j8+YwfMgS/3+91iO2mYkxSz+/3M3vyZEbm53Na376MzM9n9uTJ\nGfFzLpLu1JOQgm6eOZNpNTWMDgQa2gwYHQjgamq4ZdYsSisqvAuwHVSMSRqrT4in1dRQGghggAOW\nzp/P+GXLWFhVlTE9ZyLpSD0JKWj54sWMapQgNDY6EGD5okVJjig+zMIThJoaJQgdXeOEuH6QoT4h\nnhpKiEXEO0oSUoxzji51dbQ0KmtATl1dWk1mzPRiTOn0d5FqMjUhFskUGm5IMWbGtqwsHERMFByw\nLSsrLSZ3ffop7LtveFumFGPqCBNLEy2ahDgdft5FMlEG/LrOPEPHjmVpC5+kz/h8DCsuTnJE0TML\nTxAyqRhTR5hYmgyNE+JI0ikhFslUGfArO/PMKC9nbkEBS3y+hl+gDlji8zGvoIDpZWVehrdbK1ZE\nXvPgnHO8iScRNI4eP5mQEItkMiUJKSg3N5eFVVWsnDSJU/LyGNenD6fk5bFy0qSUne3tXDA5GDp0\nV9vHH2fmxESNo8dPOifEIh2B5iSkqNzc3OBjjhUVKT8m+/Ofw5w5u7bnzoWpU72LJ5E0jh5f9Qnx\nLbNmMXfRInLq6tielcXQ4mIWlpWlZEIs0pEoSUgDqfph89//wv77h7dlYs9BY5k0sTRVpFNCLNLR\naLhBYpKVFZ4gvPlm5icI9TSOnjhKEERSi5IEicqf/xyce/DNN8HtMWOCycEhh4Tvl8lrB2gcXUQ6\nCg03SJvU1cGee4a3ffVVeFtHWTtA4+gi0lFYKn7jM7NCoLq6uprCwkKvw+nwJkyAxx7btf3HP8KZ\nZ4bv03gN/lGN1+D3+ZhbUJCyT2XEg8bRRSSVrFq1iqKiIoAi59yq9pxLPQnSorffbr50cks5ZSYX\npWqNEgQRyVSakyARmYUnCB99tPuJiVo7QEQk8yhJkDDz54evmDhlSjA56NOn5WMysSiViIhouEFC\ntm6FplMG2lqMSWsHiIhkpqh7EsxsuJktMrONZhYws90+FG5mx4X2a/zaaWa9Yg9b4umww8IThOef\nj74Yk9YOEBHJPLEMN3QBXgV+Ci0WcGvKAQcBvUOv/Z1zH8dwbYmj+mJMr78e3B40KJgcHHdc9OfS\n2gEiIpkn6uEG59wzwDMAFl3/8SfOuS+ivZ7EX6Regs8/h+7dYz+n1g4QEck8yZqTYMCrZpYNvAmU\nOudWJOna0kgiizFpDX4RkcySjCThP8CPgVeAzsAlwPNmdpRz7tUkXF9IfjEmJQgiIukv4UmCc+4d\n4J1GTX83swHAVOC8RF9fgsWY6mstQLAYU9NaCyIiIk159QjkP4Chre00depUujcZKC8pKaGkpCRR\ncWWUP/8ZfvjDXdtjxsDixd7FIyIi8VVZWUllZWVYW21tbdzO367aDWYWAE5zzkW1nJ6Z/R/whXPu\njBbeV+2GdmhLMSYREclM8azdEMs6CV3M7DAzOzzU1D+03Tf0/o1mtqDR/lPMrNjMBpjZIWZ2K3AC\ncHt7ApfIJkwITwb++Mfg3AMlCCIiEq1YhhuOBP5G8DF4B9wSal8AXEhwHYS+jfbfM7TPt4HtwOvA\nSc65F2OMWSJYswYKCsLbtAqyiIi0RyzrJLzAbnognHMXNNn+NfDr6EOTtmr6IMFHH+2+1oKIiEhb\nqMBTGoulGJOIiEhbqcBTGmpPMSYREZG20sdKmolHMSYREZG2UE9CmlixAoY2Wlli0CB4+23v4hER\nkcynJCHFJaIYk4iISFuokzqF/frX4QnC3LnBpEEJgoiIJIN6ElLQp5/CvvuGt2nNAxERSTb1JKSY\nk08OTxDWrVOCICIi3lCSkCJefjm45sFzzwW3r746mBzk5XkaloiIdGAabvDYN98ESzk39vXXzdtE\nRESSTT0JHnHOUVoangz87W/B3gMlCCIikgrUk5BEfr+fm2fO5K9/rmb5R8sb2ocM+YYVK/RXISIi\nqUU9CUni9/sZP2QI//rN4LAE4Q/Wi65fHI7f7/cwOhERkeaUJCTJ1Ivu4tm33mQxPwXgd1yMw/hf\n9wlTa2q4ZdYsjyMUEREJpyQhwb7+GgYMgHv/NAOAcTxBAONi7m3YZ3QgwPJFi7wKUUREJCIlCQn0\n299C586wdm1wex15PMHpWJP9DMipq8NpQQQREUkhmi2XAB9+CP367dq+7TZ4cm4+B67/IOL+DtiW\nlYVZ0/RBRETEO+pJiCPn4IwzdiUIffvCjh1wxRUwdOxYlrZQz/kZn49hxcVJjFRERKR1ShLi5Lnn\ngsWYFi4Mbq9YARs2QHZ2cHtGeTlzCwpY4vNRP6jggCU+H/MKCpheVuZF2CIiIi1K2yQhVcbvt26F\nrl2DNRcALr002KMwZEj4frm5uSysqmLlpEmckpfHuD59OCUvj5WTJrGwqorc3NzkBy8iIrIbaTUn\noX4xouWLF9Olro5tWVkMHTuWGeXlnnzIXn89XHvtru1Nm6BXr5b3z83NpbSiAioqcM5pDoKIiKS0\ntEkS6hcjmlZTQ2kggBHsrl86fz7jly1L6rfxNWugoGDX9sMPw1lnRXcOJQgiIpLq0ma44eaZM5lW\nU8PoUIIAwUcHRwcCSVuMaOdOGD58V4Jw9NHBAk3RJggiIiLpIG2ShOWLFzMqEIj4XjIWI/rTn2CP\nPYIlnQHefBP+/nfo1CmhlxUREfFMWiQJzjm61NU1W4SoXiIXI9qyBczgzDOD27/4RXBi4iGHxP1S\nIiIiKSUt5iSYGduysnAQMVFI1GJEkybB/PnBP++5J3zyCXTrFtdLiIiIpKy06EmA5C5G9M9/BnsP\n6hOEJUvgq6+UIIiISMeSNklCMhYj+vprGDgQjjoquD1uHAQCMHp0u08tIiKSdtImSUj0YkT1xZje\nfz+4vW4dPPFEsEdBRESkI0qLOQn1ErEYUaRiTFdc0e7TioiIpL20ShIaa2+C4BxMmLCr1kLfvvDO\nO7tqLYiIiHR0aTPcEE+tFWMSERGRNO5JiMXWrdC7N2zbFty+9FK46y5vYxIREUlVHaYn4frrITd3\nV4KwaZMSBBERkd3J+J6EeBRjEhER6YgyNknYuROOP35XrYWjjgrOPVCtBRERkbbJyOGGSMWYVq5U\ngiAiIhKNjEoSVIxJREQkfjJmuKFxMaasLNi8WbUWRERE2iPtk4R//nNXrQUIFmNSrQUREZH2S9vh\nhnQqxlRZWel1CHGl+0ldmXQvoPtJZZl0L5B59xMvUScJZjbczBaZ2UYzC5hZqzWazex4M6s2sy/N\n7B0zOy+2cINefDG9ijFl2g+f7id1ZdK9gO4nlWXSvUDm3U+8xNKT0AV4FfgpNFRtbpGZ5QF/Af4K\nHAZUAPeY2ckxXBuARx4J/ve224ITE/PyYj2TiIiItCTqOQnOuWeAZwCsbVWWfgKsdc5dGdp+28yG\nAVOBZ6O9PgTLOv/2t7EcKSIiIm2VjDkJxwDPNWlbCgxJwrVFREQkRsl4uqE3sKlJ2yagm5l1ds59\nFeGYbICamppEx5YUtbW1rFq1yusw4kb3k7oy6V5A95PKMuleILPup9FnZ7trG5tzrU4raPlgswBw\nmnNu0W72eRu4zzl3U6O2U4HFwF7Oua8jHHMW8HDMgYmIiMjZzrlH2nOCZPQk/Bf4VpO2XsAXkRKE\nkKXA2cB64MvEhSYiIpJxsoE8gp+l7ZKMJKEK+H6TtlNC7RE557YA7cp+REREOrAV8ThJLOskdDGz\nw8zs8FBT/9B239D7N5rZgkaH/BYYYGY3mdlgM7scOAOY2+7oRUREJGGinpNgZscBf6P5GgkLnHMX\nmtnvgQOdcyc2OWYucDDwEfBL59yD7YpcREREEqpdExdFREQkc6Vt7QYRERFJLCUJIiIiElHKJAlm\ndrWZ/cPMvjCzTWb2ZzMb5HVcsTKzy8zsNTOrDb1WmFkK1qiMXujvKmBmaTn51Mxmh+Jv/FrtdVzt\nYWbfNrMHzWyzmW0P/ewVeh1XLMxsXYS/n4CZ/cbr2KJlZj4zu97M1ob+Xt4zs1lex9UeZtbVzG41\ns/Whe3rZzI70Oq62aEuBQjP7pZn9O3Rvz5rZQC9ibU1r92Jmp5vZM2b2Sej978ZynZRJEoDhwG+A\no4GRQBbwf2a2l6dRxe5D4OdAUei1DHjSzAo8jaqdzOx7wCXAa17H0k5vEly/o3foNczbcGJnZj2A\n5cBXwCigAJgOfOZlXO1wJLv+XnoDJxOcKP2ol0HF6Crgx8DlwHeAK4ErzWySp1G1z73ASQTXsjmU\nYA2e58xsf0+japvdFig0s58Dkwj+nR0FbAOWmtmeyQyyjVorttgFeJng51DMkw9TduKimfUEPgZG\nOOde9jqeeDCzLcAM59zvvY4lFmbWFagmWLTrF8C/nHPTvI0qemY2GxjnnEvLb9pNmdmvgCHOueO8\njiURzOxW4FTnXNr1LJrZYuC/zrlLGrU9Bmx3zp3rXWSxMbNswA+MDRX7q29/BXjaOXetZ8FFKdKK\nwWb2b+DXzrl5oe1uBMsInOecS9kkdXerH5vZgcA64HDn3OvRnjuVehKa6kEw+/nU60DaK9Tl+CMg\nh90sIpUG5gOLnXPLvA4kDg4KddO9b2YP1a/zkabGAq+Y2aOhobpVZnax10HFg5llEfzGeq/XscRo\nBXCSmR0EYGaHAUOBpz2NKnZ7AJ0I9lo1toM07o0DMLN8gj1Xf61vc859AaykAxckTMaKi1ELlaC+\nFXjZOZe2Y8VmdijBpKA++z7dObfG26hiE0pyDifYFZzu/g6cD7wN7A+UAi+a2aHOuW0exhWr/gR7\nd24BygkO2d1mZl865x7yNLL2Ox3oDixobccU9SugG7DGzHYS/GI20zn3B2/Dio1zbquZVQG/MLM1\nBL9ln0XwQ/RdT4Nrv94Ev5hGKkjYO/nhpIaUTBKAOwguvDTU60DaaQ1wGMFekfHAA2Y2It0SBTM7\ngGDSdrJzrs7reNrLOdd4PfM3zewfwAfAmUA6DgX5gH84534R2n7NzA4hmDike5JwIbDEOfdfrwOJ\n0f8S/BD9EbCaYKJdYWb/TuMF5SYC9wEbgW+AVQSX0c+I4bsIjHaM6ae7lBtuMLPbgVOB451z//E6\nnvZwzn3jnFvrnFvlnJtJcLLfFK/jikERsB9QbWZ1ZlYHHAdMMbOvQz0/acs5Vwu8A6TkLOY2+A/Q\ntK56DdDPg1jixsz6EZzE/DuvY2mHOcCNzrk/Oefecs49DMwDrvY4rpg559Y5504gODGur3PuGGBP\nguPe6ey/BBOCSAUJm/YudBgplSSEEoRxwAnOuQ1ex5MAPqCz10HE4Dngfwh+Czos9HqF4LfUw1yq\nzn5to9CEzAEEP2zT0XJgcJO2wQR7R9LZhQR/Oafr+D0E5yE1/fcRIMV+98bCObfDObfJzPYm+FTN\nE17H1B7OuXUEE4WT6ttCExePJk7FkjwU8+/olBluMLM7gBKgGNhmZvXZXK1zLu3KRZtZObCE4KOQ\nuQQnXx1HsAJmWgmN04fNDTGzbcAW51zTb7Apz8x+DSwm+CHaB7iOYLdppZdxtcM8YLmZXU3wMcGj\ngYsJPqqalkK9U+cD9zvnAh6H0x6LgZlm9iHwFsEu+anAPZ5G1Q5mdgrBb9xvAwcR7C2pAe73MKw2\nMbMuBHsM63s/+4cmk37qnPuQ4LDqLDN7D1gPXE+w3tCTHoS7W63dSyh560fwd5wB3wn9u/qvc67t\nPSPOuZR4Ecyud0Z4net1bDHezz3AWoKzfv8L/B9wotdxxfH+lgFzvY4jxtgrCf7D3wFsIDiemu91\nXO28p1OB14HtBD+MLvQ6pnbez8mhf/8DvY6lnffRhWBxu3UEn7l/l2BSuofXsbXjniYA74X+/WwE\nKoBcr+NqY+zHtfBZc1+jfUqBf4f+LS1N1Z/B1u4FOK+F96+N5jopu06CiIiIeCvtx8VEREQkMZQk\niIiISERKEkRERCQiJQkiIiISkZIEERERiUhJgoiIiESkJEFEREQiUpIgIiIiESlJEBERkYiUJIiI\niEhEShJEREQkov8PMJtz3b7pz2EAAAAASUVORK5CYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x10eee3ad0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Start training\n", "with tf.Session() as sess:\n", "    sess.run(init)\n", "\n", "    # Fit all training data\n", "    for epoch in range(training_epochs):\n", "        for (x, y) in zip(train_X, train_Y):\n", "            sess.run(optimizer, feed_dict={X: x, Y: y})\n", "\n", "        #Display logs per epoch step\n", "        if (epoch+1) % display_step == 0:\n", "            c = sess.run(cost, feed_dict={X: train_X, Y:train_Y})\n", "            print \"Epoch:\", '%04d' % (epoch+1), \"cost=\", \"{:.9f}\".format(c), \\\n", "                \"W=\", sess.run(W), \"b=\", sess.run(b)\n", "\n", "    print \"Optimization Finished!\"\n", "    training_cost = sess.run(cost, feed_dict={X: train_X, Y: train_Y})\n", "    print \"Training cost=\", training_cost, \"W=\", sess.run(W), \"b=\", sess.run(b), '\\n'\n", "\n", "    #Graphic display\n", "    plt.plot(train_X, train_Y, 'ro', label='Original data')\n", "    plt.plot(train_X, sess.run(W) * train_X + sess.run(b), label='Fitted line')\n", "    plt.legend()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAyAAAAJYCAYAAACadoJwAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nbl4AAAAASUVORK5CYII=\n", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["# Regression result"]}], "metadata": {"anaconda-cloud": {}, "kernelspec": {"display_name": "Python [default]", "language": "python", "name": "python2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.12"}}, "nbformat": 4, "nbformat_minor": 0}