{"cells": [{"cell_type": "code", "execution_count": 1, "id": "d86c9320-9b31-4c36-85ec-6a7fb119a93a", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import re,string"]}, {"cell_type": "code", "execution_count": 2, "id": "a0009a24-2ff1-4b33-85fd-2f9d3dc73383", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dattaframe size: 3725\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>question</th>\n", "      <th>answer</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>hi, how are you doing?</td>\n", "      <td>i'm fine. how about yourself?</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>i'm fine. how about yourself?</td>\n", "      <td>i'm pretty good. thanks for asking.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>i'm pretty good. thanks for asking.</td>\n", "      <td>no problem. so how have you been?</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>no problem. so how have you been?</td>\n", "      <td>i've been great. what about you?</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>i've been great. what about you?</td>\n", "      <td>i've been good. i'm in school right now.</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                              question  \\\n", "0               hi, how are you doing?   \n", "1        i'm fine. how about yourself?   \n", "2  i'm pretty good. thanks for asking.   \n", "3    no problem. so how have you been?   \n", "4     i've been great. what about you?   \n", "\n", "                                     answer  \n", "0             i'm fine. how about yourself?  \n", "1       i'm pretty good. thanks for asking.  \n", "2         no problem. so how have you been?  \n", "3          i've been great. what about you?  \n", "4  i've been good. i'm in school right now.  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["#Reading dialogs.txt file \n", "df = pd.read_csv('D:\\\\archive\\\\dialogs.txt', sep = '\\t', names = ['question','answer'])\n", "print(f'Dattaframe size: {len(df)}')\n", "df.head()"]}, {"cell_type": "code", "execution_count": 3, "id": "36c56072-8039-4a48-aa7b-6b5b226265f7", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 2000x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x600 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#Measuring length of question and answer\n", "df['question tokens'] = df['question'].apply(lambda x:len(x.split()))\n", "df['answer tokens'] = df['answer'].apply(lambda x:len(x.split()))\n", "\n", "#Plotting histogram for question and answer\n", "plt.style.use('fivethirtyeight')\n", "fig,ax = plt.subplots(nrows = 1, ncols = 2, figsize = (20,5))\n", "sns.set_palette('Set2')\n", "sns.histplot(x = df['question tokens'], data = df, kde = True, ax = ax[0])\n", "sns.histplot(x = df['answer tokens'], data = df, kde = True, ax = ax[1])\n", "\n", "#Joining plots of question and answer\n", "sns.jointplot(x = 'question tokens', y = 'answer tokens', data = df, kind = 'kde', fill = True, cmap = 'YlGnBu')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 4, "id": "bdd44eed-30b8-4f82-867f-29984ea7b384", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>question</th>\n", "      <th>answer</th>\n", "      <th>encoder_inputs</th>\n", "      <th>decoder_targets</th>\n", "      <th>decoder_inputs</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>hi, how are you doing?</td>\n", "      <td>i'm fine. how about yourself?</td>\n", "      <td>hi ,  how are you doing ?</td>\n", "      <td>i ' m fine .  how about yourself ? &lt;end&gt;</td>\n", "      <td>&lt;start&gt;i ' m fine .  how about yourself ? &lt;end&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>i'm fine. how about yourself?</td>\n", "      <td>i'm pretty good. thanks for asking.</td>\n", "      <td>i ' m fine .  how about yourself ?</td>\n", "      <td>i ' m pretty good .  thanks for asking . &lt;end&gt;</td>\n", "      <td>&lt;start&gt;i ' m pretty good .  thanks for asking ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>i'm pretty good. thanks for asking.</td>\n", "      <td>no problem. so how have you been?</td>\n", "      <td>i ' m pretty good .  thanks for asking .</td>\n", "      <td>no problem .  so how have you been ? &lt;end&gt;</td>\n", "      <td>&lt;start&gt;no problem .  so how have you been ? &lt;end&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>no problem. so how have you been?</td>\n", "      <td>i've been great. what about you?</td>\n", "      <td>no problem .  so how have you been ?</td>\n", "      <td>i ' ve been great .  what about you ? &lt;end&gt;</td>\n", "      <td>&lt;start&gt;i ' ve been great .  what about you ? &lt;...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>i've been great. what about you?</td>\n", "      <td>i've been good. i'm in school right now.</td>\n", "      <td>i ' ve been great .  what about you ?</td>\n", "      <td>i ' ve been good .  i ' m in school right now ...</td>\n", "      <td>&lt;start&gt;i ' ve been good .  i ' m in school rig...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>i've been good. i'm in school right now.</td>\n", "      <td>what school do you go to?</td>\n", "      <td>i ' ve been good .  i ' m in school right now .</td>\n", "      <td>what school do you go to ? &lt;end&gt;</td>\n", "      <td>&lt;start&gt;what school do you go to ? &lt;end&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>what school do you go to?</td>\n", "      <td>i go to pcc.</td>\n", "      <td>what school do you go to ?</td>\n", "      <td>i go to pcc . &lt;end&gt;</td>\n", "      <td>&lt;start&gt;i go to pcc . &lt;end&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>i go to pcc.</td>\n", "      <td>do you like it there?</td>\n", "      <td>i go to pcc .</td>\n", "      <td>do you like it there ? &lt;end&gt;</td>\n", "      <td>&lt;start&gt;do you like it there ? &lt;end&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>do you like it there?</td>\n", "      <td>it's okay. it's a really big campus.</td>\n", "      <td>do you like it there ?</td>\n", "      <td>it ' s okay .  it ' s a really big campus . &lt;end&gt;</td>\n", "      <td>&lt;start&gt;it ' s okay .  it ' s a really big camp...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>it's okay. it's a really big campus.</td>\n", "      <td>good luck with school.</td>\n", "      <td>it ' s okay .  it ' s a really big campus .</td>\n", "      <td>good luck with school . &lt;end&gt;</td>\n", "      <td>&lt;start&gt;good luck with school . &lt;end&gt;</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   question  \\\n", "0                    hi, how are you doing?   \n", "1             i'm fine. how about yourself?   \n", "2       i'm pretty good. thanks for asking.   \n", "3         no problem. so how have you been?   \n", "4          i've been great. what about you?   \n", "5  i've been good. i'm in school right now.   \n", "6                 what school do you go to?   \n", "7                              i go to pcc.   \n", "8                     do you like it there?   \n", "9      it's okay. it's a really big campus.   \n", "\n", "                                     answer  \\\n", "0             i'm fine. how about yourself?   \n", "1       i'm pretty good. thanks for asking.   \n", "2         no problem. so how have you been?   \n", "3          i've been great. what about you?   \n", "4  i've been good. i'm in school right now.   \n", "5                 what school do you go to?   \n", "6                              i go to pcc.   \n", "7                     do you like it there?   \n", "8      it's okay. it's a really big campus.   \n", "9                    good luck with school.   \n", "\n", "                                     encoder_inputs  \\\n", "0                        hi ,  how are you doing ?    \n", "1               i ' m fine .  how about yourself ?    \n", "2         i ' m pretty good .  thanks for asking .    \n", "3             no problem .  so how have you been ?    \n", "4            i ' ve been great .  what about you ?    \n", "5  i ' ve been good .  i ' m in school right now .    \n", "6                       what school do you go to ?    \n", "7                                    i go to pcc .    \n", "8                           do you like it there ?    \n", "9      it ' s okay .  it ' s a really big campus .    \n", "\n", "                                     decoder_targets  \\\n", "0           i ' m fine .  how about yourself ? <end>   \n", "1     i ' m pretty good .  thanks for asking . <end>   \n", "2         no problem .  so how have you been ? <end>   \n", "3        i ' ve been great .  what about you ? <end>   \n", "4  i ' ve been good .  i ' m in school right now ...   \n", "5                   what school do you go to ? <end>   \n", "6                                i go to pcc . <end>   \n", "7                       do you like it there ? <end>   \n", "8  it ' s okay .  it ' s a really big campus . <end>   \n", "9                      good luck with school . <end>   \n", "\n", "                                      decoder_inputs  \n", "0    <start>i ' m fine .  how about yourself ? <end>  \n", "1  <start>i ' m pretty good .  thanks for asking ...  \n", "2  <start>no problem .  so how have you been ? <end>  \n", "3  <start>i ' ve been great .  what about you ? <...  \n", "4  <start>i ' ve been good .  i ' m in school rig...  \n", "5            <start>what school do you go to ? <end>  \n", "6                         <start>i go to pcc . <end>  \n", "7                <start>do you like it there ? <end>  \n", "8  <start>it ' s okay .  it ' s a really big camp...  \n", "9               <start>good luck with school . <end>  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["def clean_text(text):\n", "    text = re.sub('-', ' ', text.lower())\n", "    text = re.sub('[.]', ' . ', text)\n", "    text = re.sub('[1]', ' 1 ', text)\n", "    text = re.sub('[2]', ' 2 ', text)\n", "    text = re.sub('[3]', ' 3 ', text)\n", "    text = re.sub('[4]', ' 5 ', text)\n", "    text = re.sub('[6]', ' 6 ', text)\n", "    text = re.sub('[7]', ' 7 ', text)\n", "    text = re.sub('[8]', ' 8 ', text)\n", "    text = re.sub('[9]', ' 9 ', text)\n", "    text = re.sub('[0]', ' 0 ', text)\n", "    text = re.sub('[,]', ' , ', text)\n", "    text = re.sub('[?]', ' ? ', text)\n", "    text = re.sub('[!]', ' ! ', text)\n", "    text = re.sub('[$]', ' $ ', text)\n", "    text = re.sub('[&]', ' & ', text)\n", "    text = re.sub('[/]', ' / ', text)\n", "    text = re.sub('[:]', ' : ', text)\n", "    text = re.sub('[;]', ' ; ', text)\n", "    text = re.sub('[*]', ' * ', text)\n", "    text = re.sub('[\\']', ' \\' ', text)\n", "    text = re.sub('[\\\"]', ' \\\" ', text)\n", "    text = re.sub('[\\t]', ' \\t ', text)\n", "    return text\n", "\n", "df.drop(columns = ['answer tokens', 'question tokens'], axis = 1, inplace = True)\n", "df['encoder_inputs'] = df['question'].apply(clean_text)\n", "df['decoder_targets'] = df['answer'].apply(clean_text) + '<end>'\n", "df['decoder_inputs'] = '<start>' + df['answer'].apply(clean_text) + '<end>'\n", "\n", "df.head(10)"]}, {"cell_type": "code", "execution_count": 3, "id": "ac37fe1d-b1a3-489b-8999-9eb314c92c1c", "metadata": {}, "outputs": [], "source": ["def chatbot_response(text):\n", "    if text == 'hi':\n", "        return 'hello'"]}, {"cell_type": "code", "execution_count": 4, "id": "08efd7ba-c208-4f71-aa92-d5d9423ed3a7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" * Serving Flask app '__main__'\n", " * Debug mode: off\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.\n", " * Running on http://127.0.0.1:5000\n", "Press CTRL+C to quit\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2023-10-25 18:21:22,489] ERROR in app: Exception on / [GET]\n", "Traceback (most recent call last):\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py\", line 1455, in wsgi_app\n", "    response = self.full_dispatch_request()\n", "               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py\", line 869, in full_dispatch_request\n", "    rv = self.handle_user_exception(e)\n", "         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py\", line 867, in full_dispatch_request\n", "    rv = self.dispatch_request()\n", "         ^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py\", line 852, in dispatch_request\n", "    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20496\\3752683497.py\", line 9, in index\n", "    return render_template(\"index.html\")\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py\", line 151, in render_template\n", "    template = app.jinja_env.get_or_select_template(template_name_or_list)\n", "               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py\", line 1081, in get_or_select_template\n", "    return self.get_template(template_name_or_list, parent, globals)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py\", line 1010, in get_template\n", "    return self._load_template(name, globals)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py\", line 969, in _load_template\n", "    template = self.loader.load(self, name, self.make_globals(globals))\n", "               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py\", line 126, in load\n", "    source, filename, uptodate = self.get_source(environment, name)\n", "                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py\", line 65, in get_source\n", "    return self._get_source_fast(environment, template)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py\", line 99, in _get_source_fast\n", "    raise TemplateNotFound(template)\n", "jinja2.exceptions.TemplateNotFound: index.html\n", "127.0.0.1 - - [25/Oct/2023 18:21:22] \"GET / HTTP/1.1\" 500 -\n", "127.0.0.1 - - [25/Oct/2023 18:21:22] \"GET /favicon.ico HTTP/1.1\" 404 -\n"]}], "source": ["from flask import Flask, render_template, request\n", "\n", "app = Flask(__name__)\n", "app.static_folder = 'static'\n", "\n", "@app.route(\"/\")\n", "def home():\n", "    return render_template(\"index.html\")\n", "\n", "@app.route(\"/get\")\n", "def get_bot_response():\n", "    userText = request.args.get('msg')\n", "    return chatbot_response(userText)\n", "\n", "if __name__ == \"__main__\":\n", "    app.run()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}