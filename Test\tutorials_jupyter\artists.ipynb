{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["\n", ".. redirect-from:: /tutorials/intermediate/artists\n", "\n", "\n", "# Artist tutorial\n", "\n", "Using Artist objects to render on the canvas.\n", "\n", "There are three layers to the Matplotlib API.\n", "\n", "* the :class:`matplotlib.backend_bases.FigureCanvas` is the area onto which\n", "  the figure is drawn\n", "* the :class:`matplotlib.backend_bases.Renderer` is the object which knows how\n", "  to draw on the :class:`~matplotlib.backend_bases.FigureCanvas`\n", "* and the :class:`matplotlib.artist.Artist` is the object that knows how to use\n", "  a renderer to paint onto the canvas.\n", "\n", "The :class:`~matplotlib.backend_bases.FigureCanvas` and\n", ":class:`~matplotlib.backend_bases.Renderer` handle all the details of\n", "talking to user interface toolkits like [wxPython](https://www.wxpython.org) or drawing languages like PostScript®, and\n", "the ``Artist`` handles all the high level constructs like representing\n", "and laying out the figure, text, and lines.  The typical user will\n", "spend 95% of their time working with the ``Artists``.\n", "\n", "There are two types of ``Artists``: primitives and containers.  The primitives\n", "represent the standard graphical objects we want to paint onto our canvas:\n", ":class:`~matplotlib.lines.Line2D`, :class:`~matplotlib.patches.Rectangle`,\n", ":class:`~matplotlib.text.Text`, :class:`~matplotlib.image.AxesImage`, etc., and\n", "the containers are places to put them (:class:`~matplotlib.axis.Axis`,\n", ":class:`~matplotlib.axes.Axes` and :class:`~matplotlib.figure.Figure`).  The\n", "standard use is to create a :class:`~matplotlib.figure.Figure` instance, use\n", "the ``Figure`` to create one or more :class:`~matplotlib.axes.Axes`\n", "instances, and use the ``Axes`` instance\n", "helper methods to create the primitives.  In the example below, we create a\n", "``Figure`` instance using :func:`matplotlib.pyplot.figure`, which is a\n", "convenience method for instantiating ``Figure`` instances and connecting them\n", "with your user interface or drawing toolkit ``FigureCanvas``.  As we will\n", "discuss below, this is not necessary -- you can work directly with PostScript,\n", "PDF Gtk+, or wxPython ``FigureCanvas`` instances, instantiate your ``Figures``\n", "directly and connect them yourselves -- but since we are focusing here on the\n", "``Artist`` API we'll let :mod:`~matplotlib.pyplot` handle some of those details\n", "for us::\n", "\n", "    import matplotlib.pyplot as plt\n", "    fig = plt.figure()\n", "    ax = fig.add_subplot(2, 1, 1) # two rows, one column, first plot\n", "\n", "The :class:`~matplotlib.axes.Axes` is probably the most important\n", "class in the Matplotlib API, and the one you will be working with most\n", "of the time.  This is because the ``Axes`` is the plotting area into\n", "which most of the objects go, and the ``Axes`` has many special helper\n", "methods (:meth:`~matplotlib.axes.Axes.plot`,\n", ":meth:`~matplotlib.axes.Axes.text`,\n", ":meth:`~matplotlib.axes.Axes.hist`,\n", ":meth:`~matplotlib.axes.Axes.imshow`) to create the most common\n", "graphics primitives (:class:`~matplotlib.lines.Line2D`,\n", ":class:`~matplotlib.text.Text`,\n", ":class:`~matplotlib.patches.Rectangle`,\n", ":class:`~matplotlib.image.AxesImage`, respectively).  These helper methods\n", "will take your data (e.g., ``numpy`` arrays and strings) and create\n", "primitive ``Artist`` instances as needed (e.g., ``Line2D``), add them to\n", "the relevant containers, and draw them when requested.  If you want to create\n", "an ``Axes`` at an arbitrary location, simply use the\n", ":meth:`~matplotlib.figure.Figure.add_axes` method which takes a list\n", "of ``[left, bottom, width, height]`` values in 0-1 relative figure\n", "coordinates::\n", "\n", "    fig2 = plt.figure()\n", "    ax2 = fig2.add_axes([0.15, 0.1, 0.7, 0.3])\n", "\n", "Continuing with our example::\n", "\n", "    import numpy as np\n", "    t = np.arange(0.0, 1.0, 0.01)\n", "    s = np.sin(2*np.pi*t)\n", "    line, = ax.plot(t, s, color='blue', lw=2)\n", "\n", "In this example, ``ax`` is the ``Axes`` instance created by the\n", "``fig.add_subplot`` call above and when you call ``ax.plot``, it creates a\n", "``Line2D`` instance and\n", "adds it to the ``Axes``.  In the interactive [IPython](https://ipython.org/)\n", "session below, you can see that the ``Axes.lines`` list is length one and\n", "contains the same line that was returned by the ``line, = ax.plot...`` call:\n", "\n", ".. sourcecode:: ipython\n", "\n", "    In [101]: ax.lines[0]\n", "    Out[101]: <matplotlib.lines.Line2D at 0x19a95710>\n", "\n", "    In [102]: line\n", "    Out[102]: <matplotlib.lines.Line2D at 0x19a95710>\n", "\n", "If you make subsequent calls to ``ax.plot`` (and the hold state is \"on\"\n", "which is the default) then additional lines will be added to the list.\n", "You can remove a line later by calling its ``remove`` method::\n", "\n", "    line = ax.lines[0]\n", "    line.remove()\n", "\n", "The Axes also has helper methods to configure and decorate the x-axis\n", "and y-axis tick, tick labels and axis labels::\n", "\n", "    xtext = ax.set_xlabel('my xdata')  # returns a Text instance\n", "    ytext = ax.set_ylabel('my ydata')\n", "\n", "When you call :meth:`ax.set_xlabel <matplotlib.axes.Axes.set_xlabel>`,\n", "it passes the information on the :class:`~matplotlib.text.Text`\n", "instance of the :class:`~matplotlib.axis.XAxis`.  Each ``Axes``\n", "instance contains an :class:`~matplotlib.axis.XAxis` and a\n", ":class:`~matplotlib.axis.YAxis` instance, which handle the layout and\n", "drawing of the ticks, tick labels and axis labels.\n", "\n", "Try creating the figure below.\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "fig = plt.figure()\n", "fig.subplots_adjust(top=0.8)\n", "ax1 = fig.add_subplot(211)\n", "ax1.set_ylabel('Voltage [V]')\n", "ax1.set_title('A sine wave')\n", "\n", "t = np.arange(0.0, 1.0, 0.01)\n", "s = np.sin(2*np.pi*t)\n", "line, = ax1.plot(t, s, color='blue', lw=2)\n", "\n", "# Fixing random state for reproducibility\n", "np.random.seed(19680801)\n", "\n", "ax2 = fig.add_axes([0.15, 0.1, 0.7, 0.3])\n", "n, bins, patches = ax2.hist(np.random.randn(1000), 50,\n", "                            facecolor='yellow', edgecolor='yellow')\n", "ax2.set_xlabel('Time [s]')\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "## Customizing your objects\n", "\n", "Every element in the figure is represented by a <PERSON><PERSON><PERSON><PERSON><PERSON>\n", ":class:`~matplotlib.artist.Artist`, and each has an extensive list of\n", "properties to configure its appearance.  The figure itself contains a\n", ":class:`~matplotlib.patches.Rectangle` exactly the size of the figure,\n", "which you can use to set the background color and transparency of the\n", "figures.  Likewise, each :class:`~matplotlib.axes.Axes` bounding box\n", "(the standard white box with black edges in the typical Matplotlib\n", "plot, has a ``Rectangle`` instance that determines the color,\n", "transparency, and other properties of the Axes.  These instances are\n", "stored as member variables :attr:`Figure.patch\n", "<matplotlib.figure.Figure.patch>` and :attr:`Axes.patch\n", "<matplotlib.axes.Axes.patch>` (\"<PERSON>\" is a name inherited from\n", "MATLAB, and is a 2D \"patch\" of color on the figure, e.g., rectangles,\n", "circles and polygons).  Every Matplotlib ``Artist`` has the following\n", "properties\n", "\n", "==========  =================================================================\n", "Property    Description\n", "==========  =================================================================\n", "alpha       The transparency - a scalar from 0-1\n", "animated    A boolean that is used to facilitate animated drawing\n", "axes        The Axes that the Artist lives in, possibly None\n", "clip_box    The bounding box that clips the Artist\n", "clip_on     Whether clipping is enabled\n", "clip_path   The path the artist is clipped to\n", "contains    A picking function to test whether the artist contains the pick\n", "            point\n", "figure      The figure instance the artist lives in, possibly None\n", "label       A text label (e.g., for auto-labeling)\n", "picker      A python object that controls object picking\n", "transform   The transformation\n", "visible     A boolean whether the artist should be drawn\n", "zorder      A number which determines the drawing order\n", "rasterized  Boolean; Turns vectors into raster graphics (for compression &\n", "            EPS transparency)\n", "==========  =================================================================\n", "\n", "Each of the properties is accessed with an old-fashioned setter or\n", "getter (yes we know this irritates Pythonista<PERSON> and we plan to support\n", "direct access via properties or traits but it hasn't been done yet).\n", "For example, to multiply the current alpha by a half::\n", "\n", "    a = o.get_alpha()\n", "    o.set_alpha(0.5*a)\n", "\n", "If you want to set a number of properties at once, you can also use\n", "the ``set`` method with keyword arguments.  For example::\n", "\n", "    o.set(alpha=0.5, zorder=2)\n", "\n", "If you are working interactively at the python shell, a handy way to\n", "inspect the ``Artist`` properties is to use the\n", ":func:`matplotlib.artist.getp` function (simply\n", ":func:`~matplotlib.pyplot.getp` in pyplot), which lists the properties\n", "and their values.  This works for classes derived from ``Artist`` as\n", "well, e.g., ``Figure`` and ``Rectangle``.  Here are the ``Figure`` rectangle\n", "properties mentioned above:\n", "\n", ".. sourcecode:: ipython\n", "\n", "    In [149]: matplotlib.artist.getp(fig.patch)\n", "      agg_filter = None\n", "      alpha = None\n", "      animated = False\n", "      antialiased or aa = False\n", "      bbox = Bbox(x0=0.0, y0=0.0, x1=1.0, y1=1.0)\n", "      capstyle = butt\n", "      children = []\n", "      clip_box = None\n", "      clip_on = True\n", "      clip_path = None\n", "      contains = None\n", "      data_transform = BboxTransformTo(     TransformedBbox(         Bbox...\n", "      edgecolor or ec = (1.0, 1.0, 1.0, 1.0)\n", "      extents = Bbox(x0=0.0, y0=0.0, x1=640.0, y1=480.0)\n", "      facecolor or fc = (1.0, 1.0, 1.0, 1.0)\n", "      figure = Figure(640x480)\n", "      fill = True\n", "      gid = None\n", "      hatch = None\n", "      height = 1\n", "      in_layout = False\n", "      joinstyle = miter\n", "      label =\n", "      linestyle or ls = solid\n", "      linewidth or lw = 0.0\n", "      patch_transform = CompositeGenericTransform(     BboxTransformTo(   ...\n", "      path = Path(array([[0., 0.],        [1., 0.],        [1.,...\n", "      path_effects = []\n", "      picker = None\n", "      rasterized = None\n", "      sketch_params = None\n", "      snap = None\n", "      transform = CompositeGenericTransform(     CompositeGenericTra...\n", "      transformed_clip_path_and_affine = (None, None)\n", "      url = None\n", "      verts = [[  0.   0.]  [640.   0.]  [640. 480.]  [  0. 480....\n", "      visible = True\n", "      width = 1\n", "      window_extent = Bbox(x0=0.0, y0=0.0, x1=640.0, y1=480.0)\n", "      x = 0\n", "      xy = (0, 0)\n", "      y = 0\n", "      zorder = 1\n", "\n", "The docstrings for all of the classes also contain the ``Artist``\n", "properties, so you can consult the interactive \"help\" or the\n", "`artist-api` for a listing of properties for a given object.\n", "\n", "\n", "## Object containers\n", "\n", "\n", "Now that we know how to inspect and set the properties of a given\n", "object we want to configure, we need to know how to get at that object.\n", "As mentioned in the introduction, there are two kinds of objects:\n", "primitives and containers.  The primitives are usually the things you\n", "want to configure (the font of a :class:`~matplotlib.text.Text`\n", "instance, the width of a :class:`~matplotlib.lines.Line2D`) although\n", "the containers also have some properties as well -- for example the\n", ":class:`~matplotlib.axes.Axes` :class:`~matplotlib.artist.Artist` is a\n", "container that contains many of the primitives in your plot, but it\n", "also has properties like the ``xscale`` to control whether the xaxis\n", "is 'linear' or 'log'.  In this section we'll review where the various\n", "container objects store the ``Artists`` that you want to get at.\n", "\n", "\n", "### Figure container\n", "\n", "The top level container ``Artist`` is the\n", ":class:`matplotlib.figure.Figure`, and it contains everything in the\n", "figure.  The background of the figure is a\n", ":class:`~matplotlib.patches.Rectangle` which is stored in\n", ":attr:`Figure.patch <matplotlib.figure.Figure.patch>`.  As\n", "you add subplots (:meth:`~matplotlib.figure.Figure.add_subplot`) and\n", "Axes (:meth:`~matplotlib.figure.Figure.add_axes`) to the figure\n", "these will be appended to the :attr:`Figure.axes\n", "<matplotlib.figure.Figure.axes>`.  These are also returned by the\n", "methods that create them:\n", "\n", ".. sourcecode:: ipython\n", "\n", "    In [156]: fig = plt.figure()\n", "\n", "    In [157]: ax1 = fig.add_subplot(211)\n", "\n", "    In [158]: ax2 = fig.add_axes([0.1, 0.1, 0.7, 0.3])\n", "\n", "    In [159]: ax1\n", "    Out[159]: <Axes:>\n", "\n", "    In [160]: print(fig.axes)\n", "    [<Axes:>, <matplotlib.axes._axes.Axes object at 0x7f0768702be0>]\n", "\n", "Because the figure maintains the concept of the \"current Axes\" (see\n", ":meth:`Figure.gca <matplotlib.figure.Figure.gca>` and\n", ":meth:`Figure.sca <matplotlib.figure.Figure.sca>`) to support the\n", "pylab/pyplot state machine, you should not insert or remove Axes\n", "directly from the Axes list, but rather use the\n", ":meth:`~matplotlib.figure.Figure.add_subplot` and\n", ":meth:`~matplotlib.figure.Figure.add_axes` methods to insert, and the\n", "`Axes.remove <matplotlib.artist.Artist.remove>` method to delete.  You are\n", "free however, to iterate over the list of Axes or index into it to get\n", "access to ``Axes`` instances you want to customize.  Here is an\n", "example which turns all the Axes grids on::\n", "\n", "    for ax in fig.axes:\n", "        ax.grid(True)\n", "\n", "\n", "The figure also has its own ``images``, ``lines``, ``patches`` and ``text``\n", "attributes, which you can use to add primitives directly. When doing so, the\n", "default coordinate system for the ``Figure`` will simply be in pixels (which\n", "is not usually what you want). If you instead use Figure-level methods to add\n", "Artists (e.g., using `.Figure.text` to add text), then the default coordinate\n", "system will be \"figure coordinates\" where (0, 0) is the bottom-left of the\n", "figure and (1, 1) is the top-right of the figure.\n", "\n", "As with all ``Artist``\\s, you can control this coordinate system by setting\n", "the transform property. You can explicitly use \"figure coordinates\" by\n", "setting the ``Artist`` transform to :attr:`fig.transFigure\n", "<matplotlib.figure.Figure.transFigure>`:\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.lines as lines\n", "\n", "fig = plt.figure()\n", "\n", "l1 = lines.Line2D([0, 1], [0, 1], transform=fig.transFigure, figure=fig)\n", "l2 = lines.Line2D([0, 1], [1, 0], transform=fig.transFigure, figure=fig)\n", "fig.lines.extend([l1, l2])\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here is a summary of the Artists the Figure contains\n", "\n", "================ ============================================================\n", "Figure attribute Description\n", "================ ============================================================\n", "axes             A list of `~.axes.Axes` instances\n", "patch            The `.Rectangle` background\n", "images           A list of `.FigureImage` patches -\n", "                 useful for raw pixel display\n", "legends          A list of Figure `.Legend` instances\n", "                 (different from ``Axes.get_legend()``)\n", "lines            A list of Figure `.Line2D` instances\n", "                 (rarely used, see ``Axes.lines``)\n", "patches          A list of Figure `.Patch`\\s\n", "                 (rarely used, see ``Axes.patches``)\n", "texts            A list Figure `.Text` instances\n", "================ ============================================================\n", "\n", "\n", "### Axes container\n", "\n", "The :class:`matplotlib.axes.Axes` is the center of the Matplotlib\n", "universe -- it contains the vast majority of all the ``Artists`` used\n", "in a figure with many helper methods to create and add these\n", "``Artists`` to itself, as well as helper methods to access and\n", "customize the ``Artists`` it contains.  Like the\n", ":class:`~matplotlib.figure.Figure`, it contains a\n", ":class:`~matplotlib.patches.Patch`\n", ":attr:`~matplotlib.axes.Axes.patch` which is a\n", ":class:`~matplotlib.patches.Rectangle` for Cartesian coordinates and a\n", ":class:`~matplotlib.patches.Circle` for polar coordinates; this patch\n", "determines the shape, background and border of the plotting region::\n", "\n", "    ax = fig.add_subplot()\n", "    rect = ax.patch  # a Rectangle instance\n", "    rect.set_facecolor('green')\n", "\n", "When you call a plotting method, e.g., the canonical\n", "`~matplotlib.axes.Axes.plot` and pass in arrays or lists of values, the\n", "method will create a `matplotlib.lines.Line2D` instance, update the line with\n", "all the ``Line2D`` properties passed as keyword arguments, add the line to\n", "the ``Axes``, and return it to you:\n", "\n", ".. sourcecode:: ipython\n", "\n", "    In [213]: x, y = np.random.rand(2, 100)\n", "\n", "    In [214]: line, = ax.plot(x, y, '-', color='blue', linewidth=2)\n", "\n", "``plot`` returns a list of lines because you can pass in multiple x, y\n", "pairs to plot, and we are unpacking the first element of the length\n", "one list into the line variable.  The line has been added to the\n", "``Axes.lines`` list:\n", "\n", ".. sourcecode:: ipython\n", "\n", "    In [229]: print(ax.lines)\n", "    [<matplotlib.lines.Line2D at 0xd378b0c>]\n", "\n", "Similarly, methods that create patches, like\n", ":meth:`~matplotlib.axes.Axes.bar` creates a list of rectangles, will\n", "add the patches to the :attr:`Axes.patches\n", "<matplotlib.axes.Axes.patches>` list:\n", "\n", ".. sourcecode:: ipython\n", "\n", "    In [233]: n, bins, rectangles = ax.hist(np.random.randn(1000), 50)\n", "\n", "    In [234]: rectangles\n", "    Out[234]: <BarContainer object of 50 artists>\n", "\n", "    In [235]: print(len(ax.patches))\n", "    Out[235]: 50\n", "\n", "You should not add objects directly to the ``Axes.lines`` or ``Axes.patches``\n", "lists, because the ``Axes`` needs to do a few things when it creates and adds\n", "an object:\n", "\n", "- It sets the ``figure`` and ``axes`` property of the ``Artist``;\n", "- It sets the default ``Axes`` transformation (unless one is already set);\n", "- It inspects the data contained in the ``Artist`` to update the data\n", "  structures controlling auto-scaling, so that the view limits can be\n", "  adjusted to contain the plotted data.\n", "\n", "You can, nonetheless, create objects yourself and add them directly to the\n", "``Axes`` using helper methods like `~matplotlib.axes.Axes.add_line` and\n", "`~matplotlib.axes.Axes.add_patch`.  Here is an annotated interactive session\n", "illustrating what is going on:\n", "\n", ".. sourcecode:: ipython\n", "\n", "    In [262]: fig, ax = plt.subplots()\n", "\n", "    # create a rectangle instance\n", "    In [263]: rect = matplotlib.patches.Rectangle((1, 1), width=5, height=12)\n", "\n", "    # by default the Axes instance is None\n", "    In [264]: print(rect.axes)\n", "    None\n", "\n", "    # and the transformation instance is set to the \"identity transform\"\n", "    In [265]: print(rect.get_data_transform())\n", "    IdentityTransform()\n", "\n", "    # now we add the Rectangle to the Axes\n", "    In [266]: ax.add_patch(rect)\n", "\n", "    # and notice that the ax.add_patch method has set the Axes\n", "    # instance\n", "    In [267]: print(rect.axes)\n", "    Axes(0.125,0.1;0.775x0.8)\n", "\n", "    # and the transformation has been set too\n", "    In [268]: print(rect.get_data_transform())\n", "    CompositeGenericTransform(\n", "        TransformWrapper(\n", "            BlendedAffine2D(\n", "                IdentityTransform(),\n", "                IdentityTransform())),\n", "        CompositeGenericTransform(\n", "            BboxTransformFrom(\n", "                TransformedBbox(\n", "                    Bbox(x0=0.0, y0=0.0, x1=1.0, y1=1.0),\n", "                    TransformWrapper(\n", "                        BlendedAffine2D(\n", "                            IdentityTransform(),\n", "                            IdentityTransform())))),\n", "            BboxTransformTo(\n", "                TransformedBbox(\n", "                    Bbox(x0=0.125, y0=0.10999999999999999, x1=0.9, y1=0.88),\n", "                    BboxTransformTo(\n", "                        TransformedBbox(\n", "                            Bbox(x0=0.0, y0=0.0, x1=6.4, y1=4.8),\n", "                            Affine2D(\n", "                                [[100.   0.   0.]\n", "                                 [  0. 100.   0.]\n", "                                 [  0.   0.   1.]])))))))\n", "\n", "    # the default Axes transformation is ax.transData\n", "    In [269]: print(ax.transData)\n", "    CompositeGenericTransform(\n", "        TransformWrapper(\n", "            BlendedAffine2D(\n", "                IdentityTransform(),\n", "                IdentityTransform())),\n", "        CompositeGenericTransform(\n", "            BboxTransformFrom(\n", "                TransformedBbox(\n", "                    Bbox(x0=0.0, y0=0.0, x1=1.0, y1=1.0),\n", "                    TransformWrapper(\n", "                        BlendedAffine2D(\n", "                            IdentityTransform(),\n", "                            IdentityTransform())))),\n", "            BboxTransformTo(\n", "                TransformedBbox(\n", "                    Bbox(x0=0.125, y0=0.10999999999999999, x1=0.9, y1=0.88),\n", "                    BboxTransformTo(\n", "                        TransformedBbox(\n", "                            Bbox(x0=0.0, y0=0.0, x1=6.4, y1=4.8),\n", "                            Affine2D(\n", "                                [[100.   0.   0.]\n", "                                 [  0. 100.   0.]\n", "                                 [  0.   0.   1.]])))))))\n", "\n", "    # notice that the xlimits of the Axes have not been changed\n", "    In [270]: print(ax.get_xlim())\n", "    (0.0, 1.0)\n", "\n", "    # but the data limits have been updated to encompass the rectangle\n", "    In [271]: print(ax.dataLim.bounds)\n", "    (1.0, 1.0, 5.0, 12.0)\n", "\n", "    # we can manually invoke the auto-scaling machinery\n", "    In [272]: ax.autoscale_view()\n", "\n", "    # and now the xlim are updated to encompass the rectangle, plus margins\n", "    In [273]: print(ax.get_xlim())\n", "    (0.75, 6.25)\n", "\n", "    # we have to manually force a figure draw\n", "    In [274]: fig.canvas.draw()\n", "\n", "\n", "There are many, many ``Axes`` helper methods for creating primitive\n", "``Artists`` and adding them to their respective containers.  The table\n", "below summarizes a small sampling of them, the kinds of ``Artist`` they\n", "create, and where they store them\n", "\n", "=========================================  =================  ===============\n", "Axes helper method                         Artist             Container\n", "=========================================  =================  ===============\n", "`~.axes.Axes.annotate` - text annotations  `.Annotation`      ax.texts\n", "`~.axes.Axes.bar` - bar charts             `.Rectangle`       ax.patches\n", "`~.axes.Axes.errorbar` - error bar plots   `.Line2D` and      ax.lines and\n", "                                           `.Rectangle`       ax.patches\n", "`~.axes.Axes.fill` - shared area           `.Polygon`         ax.patches\n", "`~.axes.Axes.hist` - histograms            `.Rectangle`       ax.patches\n", "`~.axes.Axes.imshow` - image data          `.AxesImage`       ax.images\n", "`~.axes.Axes.legend` - Axes legend         `.Legend`          ax.get_legend()\n", "`~.axes.Axes.plot` - xy plots              `.Line2D`          ax.lines\n", "`~.axes.Axes.scatter` - scatter charts     `.PolyCollection`  ax.collections\n", "`~.axes.Axes.text` - text                  `.Text`            ax.texts\n", "=========================================  =================  ===============\n", "\n", "\n", "In addition to all of these ``Artists``, the ``Axes`` contains two\n", "important ``Artist`` containers: the :class:`~matplotlib.axis.XAxis`\n", "and :class:`~matplotlib.axis.YAxis`, which handle the drawing of the\n", "ticks and labels.  These are stored as instance variables\n", ":attr:`~matplotlib.axes.Axes.xaxis` and\n", ":attr:`~matplotlib.axes.Axes.yaxis`.  The ``XAxis`` and ``YAxis``\n", "containers will be detailed below, but note that the ``Axes`` contains\n", "many helper methods which forward calls on to the\n", ":class:`~matplotlib.axis.Axis` instances, so you often do not need to\n", "work with them directly unless you want to.  For example, you can set\n", "the font color of the ``XAxis`` ticklabels using the ``Axes`` helper\n", "method::\n", "\n", "    ax.tick_params(axis='x', labelcolor='orange')\n", "\n", "Below is a summary of the Artists that the `~.axes.Axes` contains\n", "\n", "==============    =========================================\n", "Axes attribute    Description\n", "==============    =========================================\n", "artists           An `.ArtistList` of `.Artist` instances\n", "patch             `.Rectangle` instance for Axes background\n", "collections       An `.ArtistList` of `.Collection` instances\n", "images            An `.ArtistList` of `.AxesImage`\n", "lines             An `.ArtistList` of `.Line2D` instances\n", "patches           An `.ArtistList` of `.Patch` instances\n", "texts             An `.ArtistList` of `.Text` instances\n", "xaxis             A `matplotlib.axis.XAxis` instance\n", "yaxis             A `matplotlib.axis.YAxis` instance\n", "==============    =========================================\n", "\n", "The legend can be accessed by `~.axes.Axes.get_legend`,\n", "\n", "\n", "### Axis containers\n", "\n", "The :class:`matplotlib.axis.Axis` instances handle the drawing of the\n", "tick lines, the grid lines, the tick labels and the axis label.  You\n", "can configure the left and right ticks separately for the y-axis, and\n", "the upper and lower ticks separately for the x-axis.  The ``Axis``\n", "also stores the data and view intervals used in auto-scaling, panning\n", "and zooming, as well as the :class:`~matplotlib.ticker.Locator` and\n", ":class:`~matplotlib.ticker.Formatter` instances which control where\n", "the ticks are placed and how they are represented as strings.\n", "\n", "Each ``Axis`` object contains a :attr:`~matplotlib.axis.Axis.label` attribute\n", "(this is what :mod:`.pyplot` modifies in calls to `~.pyplot.xlabel` and\n", "`~.pyplot.ylabel`) as well as a list of major and minor ticks.  The ticks are\n", "`.axis.XTick` and `.axis.YTick` instances, which contain the actual line and\n", "text primitives that render the ticks and ticklabels.  Because the ticks are\n", "dynamically created as needed (e.g., when panning and zooming), you should\n", "access the lists of major and minor ticks through their accessor methods\n", "`.axis.Axis.get_major_ticks` and `.axis.Axis.get_minor_ticks`.  Although\n", "the ticks contain all the primitives and will be covered below, ``Axis``\n", "instances have accessor methods that return the tick lines, tick labels, tick\n", "locations etc.:\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["array([0. , 0.2, 0.4, 0.6, 0.8, 1. ])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots()\n", "axis = ax.xaxis\n", "axis.get_ticklocs()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["[Text(0.0, 0, '0.0'),\n", " Text(0.2, 0, '0.2'),\n", " Text(0.4, 0, '0.4'),\n", " Text(0.6000000000000001, 0, '0.6'),\n", " Text(0.8, 0, '0.8'),\n", " Text(1.0, 0, '1.0')]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["axis.get_ticklabels()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["note there are twice as many ticklines as labels because by default there are\n", "tick lines at the top and bottom but only tick labels below the xaxis;\n", "however, this can be customized.\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["<a list of 12 Line2D ticklines objects>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["axis.get_ticklines()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And with the above methods, you only get lists of major ticks back by\n", "default, but you can also ask for the minor ticks:\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["<a list of 0 Line2D ticklines objects>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["axis.get_ticklabels(minor=True)\n", "axis.get_ticklines(minor=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here is a summary of some of the useful accessor methods of the ``Axis``\n", "(these have corresponding setters where useful, such as\n", ":meth:`~matplotlib.axis.Axis.set_major_formatter`.)\n", "\n", "=============================  ==============================================\n", "Axis accessor method           Description\n", "=============================  ==============================================\n", "`~.Axis.get_scale`             The scale of the Axis, e.g., 'log' or 'linear'\n", "`~.Axis.get_view_interval`     The interval instance of the Axis view limits\n", "`~.Axis.get_data_interval`     The interval instance of the Axis data limits\n", "`~.Axis.get_gridlines`         A list of grid lines for the Axis\n", "`~.Axis.get_label`             The Axis label - a `.Text` instance\n", "`~.Axis.get_offset_text`       The Axis offset text - a `.Text` instance\n", "`~.Axis.get_ticklabels`        A list of `.Text` instances -\n", "                               keyword minor=True|False\n", "`~.Axis.get_ticklines`         A list of `.Line2D` instances -\n", "                               keyword minor=True|False\n", "`~.Axis.get_ticklocs`          A list of Tick locations -\n", "                               keyword minor=True|False\n", "`~.Axis.get_major_locator`     The `.ticker.Locator` instance for major ticks\n", "`~.Axis.get_major_formatter`   The `.ticker.Formatter` instance for major\n", "                               ticks\n", "`~.Axis.get_minor_locator`     The `.ticker.Locator` instance for minor ticks\n", "`~.Axis.get_minor_formatter`   The `.ticker.Formatter` instance for minor\n", "                               ticks\n", "`~.axis.Axis.get_major_ticks`  A list of `.Tick` instances for major ticks\n", "`~.axis.Axis.get_minor_ticks`  A list of `.Tick` instances for minor ticks\n", "`~.Axis.grid`                  Turn the grid on or off for the major or minor\n", "                               ticks\n", "=============================  ==============================================\n", "\n", "Here is an example, not recommended for its beauty, which customizes\n", "the Axes and Tick properties.\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# plt.figure creates a matplotlib.figure.Figure instance\n", "fig = plt.figure()\n", "rect = fig.patch  # a rectangle instance\n", "rect.set_facecolor('lightgoldenrodyellow')\n", "\n", "ax1 = fig.add_axes([0.1, 0.3, 0.4, 0.4])\n", "rect = ax1.patch\n", "rect.set_facecolor('lightslategray')\n", "\n", "\n", "for label in ax1.xaxis.get_ticklabels():\n", "    # label is a Text instance\n", "    label.set_color('red')\n", "    label.set_rotation(45)\n", "    label.set_fontsize(16)\n", "\n", "for line in ax1.yaxis.get_ticklines():\n", "    # line is a Line2D instance\n", "    line.set_color('green')\n", "    line.set_markersize(25)\n", "    line.set_markeredgewidth(3)\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "### Tick containers\n", "\n", "The :class:`matplotlib.axis.Tick` is the final container object in our\n", "descent from the :class:`~matplotlib.figure.Figure` to the\n", ":class:`~matplotlib.axes.Axes` to the :class:`~matplotlib.axis.Axis`\n", "to the :class:`~matplotlib.axis.Tick`.  The ``Tick`` contains the tick\n", "and grid line instances, as well as the label instances for the upper\n", "and lower ticks.  Each of these is accessible directly as an attribute\n", "of the ``Tick``.\n", "\n", "==============  ==========================================================\n", "Tick attribute  Description\n", "==============  ==========================================================\n", "tick1line       A `.Line2D` instance\n", "tick2line       A `.Line2D` instance\n", "gridline        A `.Line2D` instance\n", "label1          A `.Text` instance\n", "label2          A `.Text` instance\n", "==============  ==========================================================\n", "\n", "Here is an example which sets the formatter for the right side ticks with\n", "dollar signs and colors them green on the right side of the yaxis.\n", "\n", "\n", ".. include:: ../gallery/ticks/dollar_ticks.rst\n", "   :start-after: .. redirect-from:: /gallery/pyplots/dollar_ticks\n", "   :end-before: .. admonition:: References\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 0}