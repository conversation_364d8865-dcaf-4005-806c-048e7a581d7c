{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false}, "outputs": [], "source": ["import tensorflow as tf"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": true}, "outputs": [], "source": ["# Simple hello world using TensorFlow\n", "\n", "# Create a Constant op\n", "# The op is added as a node to the default graph.\n", "#\n", "# The value returned by the constructor represents the output\n", "# of the Constant op.\n", "\n", "hello = tf.constant('Hello, Tensor<PERSON>low!')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": true}, "outputs": [], "source": ["# Start tf session\n", "sess = tf.Session()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello, <PERSON><PERSON><PERSON><PERSON>!\n"]}], "source": ["# Run graph\n", "print(sess.run(hello))"]}], "metadata": {"kernelspec": {"display_name": "IPython (Python 2.7)", "language": "python", "name": "python2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2.0}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.8"}}, "nbformat": 4, "nbformat_minor": 0}