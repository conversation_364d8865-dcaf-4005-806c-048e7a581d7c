{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": true}, "outputs": [], "source": ["# Basic Operations example using TensorFlow library.\n", "# Author: <PERSON><PERSON><PERSON>\n", "# Project: https://github.com/aymericdamien/TensorFlow-Examples/"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": true}, "outputs": [], "source": ["import tensorflow as tf"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": true}, "outputs": [], "source": ["# Basic constant operations\n", "# The value returned by the constructor represents the output\n", "# of the Constant op.\n", "a = tf.constant(2)\n", "b = tf.constant(3)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a=2, b=3\n", "Addition with constants: 5\n", "Multiplication with constants: 6\n"]}], "source": ["# Launch the default graph.\n", "with tf.Session() as sess:\n", "    print \"a: %i\" % sess.run(a), \"b: %i\" % sess.run(b)\n", "    print \"Addition with constants: %i\" % sess.run(a+b)\n", "    print \"Multiplication with constants: %i\" % sess.run(a*b)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": true}, "outputs": [], "source": ["# Basic Operations with variable as graph input\n", "# The value returned by the constructor represents the output\n", "# of the Variable op. (define as input when running session)\n", "# tf Graph input\n", "a = tf.placeholder(tf.int16)\n", "b = tf.placeholder(tf.int16)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"collapsed": true}, "outputs": [], "source": ["# Define some operations\n", "add = tf.add(a, b)\n", "mul = tf.multiply(a, b)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Addition with variables: 5\n", "Multiplication with variables: 6\n"]}], "source": ["# Launch the default graph.\n", "with tf.Session() as sess:\n", "    # Run every operation with variable input\n", "    print \"Addition with variables: %i\" % sess.run(add, feed_dict={a: 2, b: 3})\n", "    print \"Multiplication with variables: %i\" % sess.run(mul, feed_dict={a: 2, b: 3})"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"collapsed": true}, "outputs": [], "source": ["# ----------------\n", "# More in details:\n", "# Matrix Multiplication from TensorFlow official tutorial\n", "\n", "# Create a Constant op that produces a 1x2 matrix.  The op is\n", "# added as a node to the default graph.\n", "#\n", "# The value returned by the constructor represents the output\n", "# of the Constant op.\n", "matrix1 = tf.constant([[3., 3.]])"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"collapsed": true}, "outputs": [], "source": ["# Create another Constant that produces a 2x1 matrix.\n", "matrix2 = tf.constant([[2.],[2.]])"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"collapsed": true}, "outputs": [], "source": ["# Create a Matmul op that takes 'matrix1' and 'matrix2' as inputs.\n", "# The returned value, 'product', represents the result of the matrix\n", "# multiplication.\n", "product = tf.matmul(matrix1, matrix2)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 12.]]\n"]}], "source": ["# To run the matmul op we call the session 'run()' method, passing 'product'\n", "# which represents the output of the matmul op.  This indicates to the call\n", "# that we want to get the output of the matmul op back.\n", "#\n", "# All inputs needed by the op are run automatically by the session.  They\n", "# typically are run in parallel.\n", "#\n", "# The call 'run(product)' thus causes the execution of threes ops in the\n", "# graph: the two constants and matmul.\n", "#\n", "# The output of the op is returned in 'result' as a numpy `ndarray` object.\n", "with tf.Session() as sess:\n", "    result = sess.run(product)\n", "    print result"]}], "metadata": {"kernelspec": {"display_name": "IPython (Python 2.7)", "language": "python", "name": "python2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2.0}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.8"}}, "nbformat": 4, "nbformat_minor": 0}