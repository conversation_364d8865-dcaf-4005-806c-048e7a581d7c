'''s = 'H'
n = 5
for i in range(1, n+1):
    s0 = ''
    for j in range(1, i+i):
        s0 += s
    print(s0.center(n + (n - 1), ' '))


def side():
    s1 = s*5
    for i in range(n+1):
        print(s1.center(n + (n - 1), ' '), end='')
        print(s1.rjust((n * n) - (n + (n - 1)), ' '))


side()

for i in range(n-2):
    print((s*(n*n)).rjust((n*n)+2, ' '))

side()

for i in range(0, n):
    s0 = ''
    for j in range(1, (n+n)-i-i):
        s0 += s
    print(' '*((n*n)-((n+n)-1)), end='')
    print(s0.center(n + (n - 1), ' '))'''

thickness = 5      #int(input())
c = 'H'

for i in range(thickness):
    print((c*i).rjust(thickness-1)+c+(c*1).ljust(thickness-1))