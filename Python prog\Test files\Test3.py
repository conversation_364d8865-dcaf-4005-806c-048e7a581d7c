if __name__ == '__main__':
    n = int(input())
    ls1 = []
    for i in range(n):
        str1, *line = input().split()
        ls2 = list(map(int, line))
        if str1 == 'insert': ls1.insert(ls2[0],ls2[1])
        elif str1 == 'append': ls1.append(ls2[0])
        elif str1 == 'remove': ls1.remove(ls2[0])
        elif str1 == 'pop': ls1.pop()
        elif str1 == 'sort': ls1.sort()
        elif str1 == 'reverse': ls1.reverse()
        elif str1 == 'print': print(ls1) 
        else: 
            print('error')
            break
