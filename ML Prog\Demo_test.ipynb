{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Version:  2.14.0\n"]}], "source": ["import tensorflow as tf \n", "print(\"Version: \", tf.__version__)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["x_train: (60000, 28, 28)\n", "x_test: (10000, 28, 28)\n", "y_train: (60000,)\n", "y_test: (10000,)\n", "\n", "x_train: (60000, 28, 28)\n", "x_test: (10000, 28, 28)\n", "y_train: (60000,)\n", "y_test: (10000,)\n"]}], "source": ["mnist = tf.keras.datasets.mnist\n", "\n", "(x_train, y_train), (x_test, y_test) = mnist.load_data()\n", "print('x_train: '+str(x_train.shape))\n", "print('x_test: '+str(x_test.shape))\n", "print('y_train: '+str(y_train.shape))\n", "print('y_test: '+str(y_test.shape))\n", "print()\n", "x_train, x_test = x_train/255.0, x_test/255.0\n", "print('x_train: '+str(x_train.shape))\n", "print('x_test: '+str(x_test.shape))\n", "print('y_train: '+str(y_train.shape))\n", "print('y_test: '+str(y_test.shape))\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from matplotlib import pyplot\n", "for i in range(3):\n", "    pyplot.subplot(330+1+i)\n", "    pyplot.imshow(x_train[i], cmap=pyplot.get_cmap('gray'))\n", "    pyplot.show()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["model = tf.keras.models.Sequential([\n", "    tf.keras.layers.Flatten(input_shape=(28, 28)),\n", "    tf.keras.layers.Dense(128, activation='relu'),\n", "    tf.keras.layers.Dropout(0.2),\n", "    tf.keras.layers.Dense(10, activation='softmax')\n", "])"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["model.compile(optimizer='adam',\n", "loss= 'sparse_categorical_crossentropy',\n", "metrics=['accuracy'])"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/5\n", "1875/1875 [==============================] - 3s 1ms/step - loss: 0.2959 - accuracy: 0.9141\n", "Epoch 2/5\n", "1875/1875 [==============================] - 2s 1ms/step - loss: 0.1447 - accuracy: 0.9562\n", "Epoch 3/5\n", "1875/1875 [==============================] - 2s 1ms/step - loss: 0.1072 - accuracy: 0.9676\n", "Epoch 4/5\n", "1875/1875 [==============================] - 2s 1ms/step - loss: 0.0868 - accuracy: 0.9733\n", "Epoch 5/5\n", "1875/1875 [==============================] - 2s 1ms/step - loss: 0.0727 - accuracy: 0.9776\n", "313/313 [==============================] - 0s 932us/step - loss: 0.0728 - accuracy: 0.9777\n"]}, {"data": {"text/plain": ["[0.07281715422868729, 0.9776999950408936]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["model.fit(x_train, y_train, epochs=5)\n", "model.evaluate(x_test, y_test)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for i in range(9):\n", "    pyplot.subplot(330+1+i)\n", "    pyplot.imshow(x_train[i], cmap=pyplot.get_cmap('gray'))\n", "    pyplot.show()\n", "\n", "for i in range(3):\n", "    pyplot.subplot(330+1+i)\n", "    pyplot.imshow(x_test[i], cmap=pyplot.get_cmap('gray'))\n", "    pyplot.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 2}