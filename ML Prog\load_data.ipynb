{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd \n", "import numpy as np \n", "\n", "# Make numpy values easier to read\n", "np.set_printoptions(precision=3, suppress=True)\n", "\n", "import tensorflow as tf\n", "from tensorflow.keras import layers"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# Loading dataset\n", "pd_train = pd.read_csv(\"Online_game.csv\", usecols=[6, 7, 8, 9, 10])\n", "p = pd_train.head().pop(\"Global_Sales\")\n", "#pd_train.shape"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Features and labels\n", "sales_features = pd_train.copy()\n", "sales_label = sales_features.pop(\"Global_Sales\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[41.49, 29.02,  3.77,  8.46],\n", "       [29.08,  3.58,  6.81,  0.77],\n", "       [15.85, 12.88,  3.79,  3.31],\n", "       ...,\n", "       [ 0.  ,  0.  ,  0.  ,  0.  ],\n", "       [ 0.  ,  0.01,  0.  ,  0.  ],\n", "       [ 0.01,  0.  ,  0.  ,  0.  ]])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Array \n", "sales_features = np.array(sales_features)\n", "sales_features"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["sales_model = tf.keras.Sequential([\n", "    layers.Dense(64, activation = 'relu'),\n", "    layers.Dense(1)\n", "])\n", "\n", "sales_model.compile(loss = tf.keras.losses.MeanSquaredError(), optimizer = tf.keras.optimizers.Adam())"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/10\n", "519/519 [==============================] - 0s 797us/step - loss: 5.3468e-05\n", "Epoch 2/10\n", "519/519 [==============================] - 0s 789us/step - loss: 8.7561e-04\n", "Epoch 3/10\n", "519/519 [==============================] - 0s 781us/step - loss: 7.9784e-05\n", "Epoch 4/10\n", "519/519 [==============================] - 0s 785us/step - loss: 6.7324e-05\n", "Epoch 5/10\n", "519/519 [==============================] - 0s 745us/step - loss: 7.1192e-05\n", "Epoch 6/10\n", "519/519 [==============================] - 0s 805us/step - loss: 4.1168e-05\n", "Epoch 7/10\n", "519/519 [==============================] - 0s 795us/step - loss: 4.6054e-04\n", "Epoch 8/10\n", "519/519 [==============================] - 0s 746us/step - loss: 4.5316e-04\n", "Epoch 9/10\n", "519/519 [==============================] - 0s 774us/step - loss: 6.1767e-04\n", "Epoch 10/10\n", "519/519 [==============================] - 0s 747us/step - loss: 8.4222e-05\n"]}, {"data": {"text/plain": ["<keras.src.callbacks.History at 0x21e8b5e2290>"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# Train model\n", "sales_model.fit(sales_features, sales_label, epochs=10)\n", "\n"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["# Preprocessing\n", "normalize = layers.Normalization()\n", "\n", "# Adapt Normalization\n", "normalize.adapt(sales_features)"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/10\n", "519/519 [==============================] - 1s 1ms/step - loss: 0.6551\n", "Epoch 2/10\n", "519/519 [==============================] - 1s 1ms/step - loss: 0.0090\n", "Epoch 3/10\n", "519/519 [==============================] - 1s 1ms/step - loss: 0.0024\n", "Epoch 4/10\n", "519/519 [==============================] - 1s 1ms/step - loss: 0.0011\n", "Epoch 5/10\n", "519/519 [==============================] - 1s 1ms/step - loss: 7.8286e-04\n", "Epoch 6/10\n", "519/519 [==============================] - 1s 1ms/step - loss: 5.5509e-04\n", "Epoch 7/10\n", "519/519 [==============================] - 1s 1ms/step - loss: 4.3231e-04\n", "Epoch 8/10\n", "519/519 [==============================] - 1s 1ms/step - loss: 3.9029e-04\n", "Epoch 9/10\n", "519/519 [==============================] - 1s 1ms/step - loss: 4.5968e-04\n", "Epoch 10/10\n", "519/519 [==============================] - 1s 1ms/step - loss: 5.7515e-04\n"]}, {"data": {"text/plain": ["<keras.src.callbacks.History at 0x1f48c658d50>"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["# Normalized Model\n", "norm_sales_model = tf.keras.Sequential([\n", "    normalize,\n", "    layers.Dense(64, activation='relu'),\n", "    layers.Dense(1)\n", "])\n", "\n", "norm_sales_model.compile(loss = tf.keras.losses.MeanSquaredError(),\n", "optimizer = tf.keras.optimizers.<PERSON>())\n", "\n", "norm_sales_model.fit(sales_features, sales_label, epochs = 10)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["0        82.74\n", "1        40.24\n", "2        35.82\n", "3        33.00\n", "4        31.37\n", "         ...  \n", "16593     0.01\n", "16594     0.01\n", "16595     0.01\n", "16596     0.01\n", "16597     0.01\n", "Name: Global_Sales, Length: 16598, dtype: float64"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["sales_label"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NA_Sales</th>\n", "      <th>EU_Sales</th>\n", "      <th>JP_Sales</th>\n", "      <th>Other_Sales</th>\n", "      <th>Global_Sales</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>41.49</td>\n", "      <td>29.02</td>\n", "      <td>3.77</td>\n", "      <td>8.46</td>\n", "      <td>82.74</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>29.08</td>\n", "      <td>3.58</td>\n", "      <td>6.81</td>\n", "      <td>0.77</td>\n", "      <td>40.24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>15.85</td>\n", "      <td>12.88</td>\n", "      <td>3.79</td>\n", "      <td>3.31</td>\n", "      <td>35.82</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>15.75</td>\n", "      <td>11.01</td>\n", "      <td>3.28</td>\n", "      <td>2.96</td>\n", "      <td>33.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>11.27</td>\n", "      <td>8.89</td>\n", "      <td>10.22</td>\n", "      <td>1.00</td>\n", "      <td>31.37</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   NA_Sales  EU_Sales  JP_Sales  Other_Sales  Global_Sales\n", "0     41.49     29.02      3.77         8.46         82.74\n", "1     29.08      3.58      6.81         0.77         40.24\n", "2     15.85     12.88      3.79         3.31         35.82\n", "3     15.75     11.01      3.28         2.96         33.00\n", "4     11.27      8.89     10.22         1.00         31.37"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["pd_train.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 2}