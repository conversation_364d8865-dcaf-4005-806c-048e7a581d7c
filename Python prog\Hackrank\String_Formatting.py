'''def print_formatted(number):
    for i in range(1, number + 1):
        print(i, end=' ')
        print(str(oct(i)).lstrip('0o'), end=' ')
        print(str(hex(i)).lstrip('0x').upper(), end=' ')
        print(str(bin(i)).lstrip('0b'))'''

def print_formatted(number):
    w = len(bin(number)[2:])
    for i in range(1, number+1):
        print(str(i).rjust(w),(oct(i)[2:]).rjust(w),(hex(i)[2:].upper()).rjust(w),(bin(i)[2:]).rjust(w))


if __name__ == '__main__':
    n = int(input())
    print_formatted(n)