{"cells": [{"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["# Recurrent Neural Network Example\n", "\n", "Build a recurrent neural network (LSTM) with TensorFlow.\n", "\n", "- Author: <PERSON><PERSON><PERSON>\n", "- Project: https://github.com/aymericdamien/TensorFlow-Examples/"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## RNN Overview\n", "\n", "<img src=\"http://colah.github.io/posts/2015-08-Understanding-LSTMs/img/RNN-unrolled.png\" alt=\"nn\" style=\"width: 600px;\"/>\n", "\n", "References:\n", "- [Long Short Term Memory](http://deeplearning.cs.cmu.edu/pdfs/Hochreiter97_lstm.pdf), <PERSON><PERSON>reiter & <PERSON><PERSON>, Neural Computation 9(8): 1735-1780, 1997.\n", "\n", "## MNIST Dataset Overview\n", "\n", "This example is using MNIST handwritten digits. The dataset contains 60,000 examples for training and 10,000 examples for testing. The digits have been size-normalized and centered in a fixed-size image (28x28 pixels) with values from 0 to 1. For simplicity, each image has been flattened and converted to a 1-D numpy array of 784 features (28*28).\n", "\n", "![MNIST Dataset](http://neuralnetworksanddeeplearning.com/images/mnist_100_digits.png)\n", "\n", "To classify images using a recurrent neural network, we consider every image row as a sequence of pixels. Because MNIST image shape is 28*28px, we will then handle 28 sequences of 28 timesteps for every sample.\n", "\n", "More info: http://yann.lecun.com/exdb/mnist/"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracting /tmp/data/train-images-idx3-ubyte.gz\n", "Extracting /tmp/data/train-labels-idx1-ubyte.gz\n", "Extracting /tmp/data/t10k-images-idx3-ubyte.gz\n", "Extracting /tmp/data/t10k-labels-idx1-ubyte.gz\n"]}], "source": ["from __future__ import print_function\n", "\n", "import tensorflow as tf\n", "from tensorflow.contrib import rnn\n", "\n", "# Import MNIST data\n", "from tensorflow.examples.tutorials.mnist import input_data\n", "mnist = input_data.read_data_sets(\"/tmp/data/\", one_hot=True)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": false}, "outputs": [], "source": ["# Training Parameters\n", "learning_rate = 0.001\n", "training_steps = 10000\n", "batch_size = 128\n", "display_step = 200\n", "\n", "# Network Parameters\n", "num_input = 28 # MNIST data input (img shape: 28*28)\n", "timesteps = 28 # timesteps\n", "num_hidden = 128 # hidden layer num of features\n", "num_classes = 10 # MNIST total classes (0-9 digits)\n", "\n", "# tf Graph input\n", "X = tf.placeholder(\"float\", [None, timesteps, num_input])\n", "Y = tf.placeholder(\"float\", [None, num_classes])"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": true}, "outputs": [], "source": ["# Define weights\n", "weights = {\n", "    'out': tf.Variable(tf.random_normal([num_hidden, num_classes]))\n", "}\n", "biases = {\n", "    'out': tf.Variable(tf.random_normal([num_classes]))\n", "}"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false}, "outputs": [], "source": ["def RNN(x, weights, biases):\n", "\n", "    # Prepare data shape to match `rnn` function requirements\n", "    # Current data input shape: (batch_size, timesteps, n_input)\n", "    # Required shape: 'timesteps' tensors list of shape (batch_size, n_input)\n", "\n", "    # Unstack to get a list of 'timesteps' tensors of shape (batch_size, n_input)\n", "    x = tf.unstack(x, timesteps, 1)\n", "\n", "    # Define a lstm cell with tensorflow\n", "    lstm_cell = rnn.BasicLSTMCell(num_hidden, forget_bias=1.0)\n", "\n", "    # Get lstm cell output\n", "    outputs, states = rnn.static_rnn(lstm_cell, x, dtype=tf.float32)\n", "\n", "    # Linear activation, using rnn inner loop last output\n", "    return tf.matmul(outputs[-1], weights['out']) + biases['out']"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": true}, "outputs": [], "source": ["logits = RNN(X, weights, biases)\n", "prediction = tf.nn.softmax(logits)\n", "\n", "# Define loss and optimizer\n", "loss_op = tf.reduce_mean(tf.nn.softmax_cross_entropy_with_logits(\n", "    logits=logits, labels=Y))\n", "optimizer = tf.train.GradientDescentOptimizer(learning_rate=learning_rate)\n", "train_op = optimizer.minimize(loss_op)\n", "\n", "# Evaluate model (with test logits, for dropout to be disabled)\n", "correct_pred = tf.equal(tf.argmax(prediction, 1), tf.argmax(Y, 1))\n", "accuracy = tf.reduce_mean(tf.cast(correct_pred, tf.float32))\n", "\n", "# Initialize the variables (i.e. assign their default value)\n", "init = tf.global_variables_initializer()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Step 1, Minibatch Loss= 2.6268, Training Accuracy= 0.102\n", "Step 200, Minibatch Loss= 2.0722, Training Accuracy= 0.328\n", "Step 400, Minibatch Loss= 1.9181, Training Accuracy= 0.336\n", "Step 600, Minibatch Loss= 1.8858, Training Accuracy= 0.336\n", "Step 800, Minibatch Loss= 1.7022, Training Accuracy= 0.422\n", "Step 1000, Minibatch Loss= 1.6365, Training Accuracy= 0.477\n", "Step 1200, Minibatch Loss= 1.6691, Training Accuracy= 0.516\n", "Step 1400, Minibatch Loss= 1.4626, Training Accuracy= 0.547\n", "Step 1600, Minibatch Loss= 1.4707, Training Accuracy= 0.539\n", "Step 1800, Minibatch Loss= 1.4087, Training Accuracy= 0.570\n", "Step 2000, Minibatch Loss= 1.3033, Training Accuracy= 0.570\n", "Step 2200, Minibatch Loss= 1.3773, Training Accuracy= 0.508\n", "Step 2400, Minibatch Loss= 1.3092, Training Accuracy= 0.570\n", "Step 2600, Minibatch Loss= 1.2272, Training Accuracy= 0.609\n", "Step 2800, Minibatch Loss= 1.1827, Training Accuracy= 0.633\n", "Step 3000, Minibatch Loss= 1.0453, Training Accuracy= 0.641\n", "Step 3200, Minibatch Loss= 1.0400, Training Accuracy= 0.648\n", "Step 3400, Minibatch Loss= 1.1145, Training Accuracy= 0.656\n", "Step 3600, Minibatch Loss= 0.9884, Training Accuracy= 0.688\n", "Step 3800, Minibatch Loss= 1.0395, Training Accuracy= 0.703\n", "Step 4000, Minibatch Loss= 1.0096, Training Accuracy= 0.664\n", "Step 4200, Minibatch Loss= 0.8806, Training Accuracy= 0.758\n", "Step 4400, Minibatch Loss= 0.9090, Training Accuracy= 0.766\n", "Step 4600, Minibatch Loss= 1.0060, Training Accuracy= 0.703\n", "Step 4800, Minibatch Loss= 0.8954, Training Accuracy= 0.703\n", "Step 5000, Minibatch Loss= 0.8163, Training Accuracy= 0.750\n", "Step 5200, Minibatch Loss= 0.7620, Training Accuracy= 0.773\n", "Step 5400, Minibatch Loss= 0.7388, Training Accuracy= 0.758\n", "Step 5600, Minibatch Loss= 0.7604, Training Accuracy= 0.695\n", "Step 5800, Minibatch Loss= 0.7459, Training Accuracy= 0.734\n", "Step 6000, Minibatch Loss= 0.7448, Training Accuracy= 0.734\n", "Step 6200, Minibatch Loss= 0.7208, Training Accuracy= 0.773\n", "Step 6400, Minibatch Loss= 0.6557, Training Accuracy= 0.773\n", "Step 6600, Minibatch Loss= 0.8616, Training Accuracy= 0.758\n", "Step 6800, Minibatch Loss= 0.6089, Training Accuracy= 0.773\n", "Step 7000, Minibatch Loss= 0.5020, Training Accuracy= 0.844\n", "Step 7200, Minibatch Loss= 0.5980, Training Accuracy= 0.812\n", "Step 7400, Minibatch Loss= 0.6786, Training Accuracy= 0.766\n", "Step 7600, Minibatch Loss= 0.4891, Training Accuracy= 0.859\n", "Step 7800, Minibatch Loss= 0.7042, Training Accuracy= 0.797\n", "Step 8000, Minibatch Loss= 0.4200, Training Accuracy= 0.859\n", "Step 8200, Minibatch Loss= 0.6442, Training Accuracy= 0.742\n", "Step 8400, Minibatch Loss= 0.5569, Training Accuracy= 0.828\n", "Step 8600, Minibatch Loss= 0.5838, Training Accuracy= 0.836\n", "Step 8800, Minibatch Loss= 0.5579, Training Accuracy= 0.812\n", "Step 9000, Minibatch Loss= 0.4337, Training Accuracy= 0.867\n", "Step 9200, Minibatch Loss= 0.4366, Training Accuracy= 0.844\n", "Step 9400, Minibatch Loss= 0.5051, Training Accuracy= 0.844\n", "Step 9600, Minibatch Loss= 0.5244, Training Accuracy= 0.805\n", "Step 9800, Minibatch Loss= 0.4932, Training Accuracy= 0.805\n", "Step 10000, Minibatch Loss= 0.4833, Training Accuracy= 0.852\n", "Optimization Finished!\n", "Testing Accuracy: 0.882812\n"]}], "source": ["# Start training\n", "with tf.Session() as sess:\n", "\n", "    # Run the initializer\n", "    sess.run(init)\n", "\n", "    for step in range(1, training_steps+1):\n", "        batch_x, batch_y = mnist.train.next_batch(batch_size)\n", "        # Reshape data to get 28 seq of 28 elements\n", "        batch_x = batch_x.reshape((batch_size, timesteps, num_input))\n", "        # Run optimization op (backprop)\n", "        sess.run(train_op, feed_dict={X: batch_x, Y: batch_y})\n", "        if step % display_step == 0 or step == 1:\n", "            # Calculate batch loss and accuracy\n", "            loss, acc = sess.run([loss_op, accuracy], feed_dict={X: batch_x,\n", "                                                                 Y: batch_y})\n", "            print(\"Step \" + str(step) + \", Minibatch Loss= \" + \\\n", "                  \"{:.4f}\".format(loss) + \", Training Accuracy= \" + \\\n", "                  \"{:.3f}\".format(acc))\n", "\n", "    print(\"Optimization Finished!\")\n", "\n", "    # Calculate accuracy for 128 mnist test images\n", "    test_len = 128\n", "    test_data = mnist.test.images[:test_len].reshape((-1, timesteps, num_input))\n", "    test_label = mnist.test.labels[:test_len]\n", "    print(\"Testing Accuracy:\", \\\n", "        sess.run(accuracy, feed_dict={X: test_data, Y: test_label}))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}], "metadata": {"anaconda-cloud": {}, "kernelspec": {"display_name": "Python [default]", "language": "python", "name": "python2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.12"}}, "nbformat": 4, "nbformat_minor": 0}